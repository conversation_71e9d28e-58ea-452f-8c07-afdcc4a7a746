{"name": "online-courses-platform", "version": "1.0.0", "description": "Professional online courses platform with Express.js, Firebase, and Supabase", "main": "backend/server.js", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "start": "cd backend && npm start", "build": "cd backend && npm run build && cd ../frontend && npm run build", "install:all": "npm install && cd backend && npm install && cd ../frontend && npm install", "setup:db": "cd backend && npm run setup:db", "deploy": "npm run build && firebase deploy", "test": "cd backend && npm test"}, "keywords": ["online-courses", "education", "express", "firebase", "supabase", "nodejs"], "author": "Online Courses Platform", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}