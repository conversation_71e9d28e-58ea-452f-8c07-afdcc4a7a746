const { supabase } = require('../config/database');

class Video {
  constructor(data) {
    this.id = data.id;
    this.sectionId = data.section_id;
    this.title = data.title;
    this.description = data.description;
    this.videoUrl = data.video_url;
    this.thumbnailUrl = data.thumbnail_url;
    this.duration = data.duration;
    this.orderIndex = data.order_index;
    this.isPreview = data.is_preview;
    this.createdAt = data.created_at;
    this.updatedAt = data.updated_at;
    
    // Additional fields from joins
    this.section = data.section;
    this.pdfs = data.pdfs;
    this.progress = data.progress;
  }

  static async findAll(sectionId, options = {}) {
    const { includeProgress = false, studentId = null } = options;

    let query = supabase
      .from('videos')
      .select(`
        *,
        section:course_sections!videos_section_id_fkey(id, title, course_id),
        pdfs:pdfs(id, title, description, pdf_url, file_size)
      `)
      .eq('section_id', sectionId)
      .order('order_index', { ascending: true });

    if (includeProgress && studentId) {
      query = query.select(`
        *,
        section:course_sections!videos_section_id_fkey(id, title, course_id),
        pdfs:pdfs(id, title, description, pdf_url, file_size),
        progress:video_progress!video_progress_video_id_fkey(watched_duration, is_completed, last_watched_at)
      `);
    }

    const { data, error } = await query;

    if (error) throw error;

    return data.map(video => new Video(video));
  }

  static async findById(id, options = {}) {
    const { includeProgress = false, studentId = null } = options;

    let query = supabase
      .from('videos')
      .select(`
        *,
        section:course_sections!videos_section_id_fkey(id, title, course_id),
        pdfs:pdfs(id, title, description, pdf_url, file_size)
      `)
      .eq('id', id);

    if (includeProgress && studentId) {
      query = query.select(`
        *,
        section:course_sections!videos_section_id_fkey(id, title, course_id),
        pdfs:pdfs(id, title, description, pdf_url, file_size),
        progress:video_progress!video_progress_video_id_fkey(watched_duration, is_completed, last_watched_at)
      `);
    }

    const { data, error } = await query.single();

    if (error) {
      if (error.code === 'PGRST116') return null;
      throw error;
    }

    return new Video(data);
  }

  static async create(videoData) {
    const { data, error } = await supabase
      .from('videos')
      .insert({
        section_id: videoData.sectionId,
        title: videoData.title,
        description: videoData.description,
        video_url: videoData.videoUrl,
        thumbnail_url: videoData.thumbnailUrl,
        duration: videoData.duration,
        order_index: videoData.orderIndex,
        is_preview: videoData.isPreview || false
      })
      .select()
      .single();

    if (error) throw error;

    return new Video(data);
  }

  async update(updateData) {
    const { data, error } = await supabase
      .from('videos')
      .update({
        title: updateData.title || this.title,
        description: updateData.description || this.description,
        video_url: updateData.videoUrl || this.videoUrl,
        thumbnail_url: updateData.thumbnailUrl || this.thumbnailUrl,
        duration: updateData.duration || this.duration,
        order_index: updateData.orderIndex !== undefined ? updateData.orderIndex : this.orderIndex,
        is_preview: updateData.isPreview !== undefined ? updateData.isPreview : this.isPreview
      })
      .eq('id', this.id)
      .select()
      .single();

    if (error) throw error;

    Object.assign(this, new Video(data));
    return this;
  }

  async delete() {
    const { error } = await supabase
      .from('videos')
      .delete()
      .eq('id', this.id);

    if (error) throw error;
    return true;
  }

  async getProgress(studentId) {
    const { data, error } = await supabase
      .from('video_progress')
      .select('*')
      .eq('video_id', this.id)
      .eq('student_id', studentId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') return null;
      throw error;
    }

    return data;
  }

  async updateProgress(studentId, progressData) {
    const { data, error } = await supabase
      .from('video_progress')
      .upsert({
        video_id: this.id,
        student_id: studentId,
        watched_duration: progressData.watchedDuration,
        is_completed: progressData.isCompleted,
        last_watched_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async addPdf(pdfData) {
    const { data, error } = await supabase
      .from('pdfs')
      .insert({
        video_id: this.id,
        title: pdfData.title,
        description: pdfData.description,
        pdf_url: pdfData.pdfUrl,
        file_size: pdfData.fileSize
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async removePdf(pdfId) {
    const { error } = await supabase
      .from('pdfs')
      .delete()
      .eq('id', pdfId)
      .eq('video_id', this.id);

    if (error) throw error;
    return true;
  }

  static async reorderVideos(sectionId, videoOrders) {
    // videoOrders should be an array of { id, orderIndex }
    const updates = videoOrders.map(({ id, orderIndex }) => ({
      id,
      order_index: orderIndex
    }));

    const { error } = await supabase
      .from('videos')
      .upsert(updates);

    if (error) throw error;
    return true;
  }

  static async getNextVideo(currentVideoId) {
    // Get current video to find its section and order
    const currentVideo = await Video.findById(currentVideoId);
    if (!currentVideo) return null;

    const { data, error } = await supabase
      .from('videos')
      .select('*')
      .eq('section_id', currentVideo.sectionId)
      .gt('order_index', currentVideo.orderIndex)
      .order('order_index', { ascending: true })
      .limit(1)
      .single();

    if (error) {
      if (error.code === 'PGRST116') return null;
      throw error;
    }

    return new Video(data);
  }

  static async getPreviousVideo(currentVideoId) {
    // Get current video to find its section and order
    const currentVideo = await Video.findById(currentVideoId);
    if (!currentVideo) return null;

    const { data, error } = await supabase
      .from('videos')
      .select('*')
      .eq('section_id', currentVideo.sectionId)
      .lt('order_index', currentVideo.orderIndex)
      .order('order_index', { ascending: false })
      .limit(1)
      .single();

    if (error) {
      if (error.code === 'PGRST116') return null;
      throw error;
    }

    return new Video(data);
  }

  toJSON() {
    return {
      id: this.id,
      sectionId: this.sectionId,
      section: this.section,
      title: this.title,
      description: this.description,
      videoUrl: this.videoUrl,
      thumbnailUrl: this.thumbnailUrl,
      duration: this.duration,
      orderIndex: this.orderIndex,
      isPreview: this.isPreview,
      pdfs: this.pdfs,
      progress: this.progress,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    };
  }
}

module.exports = Video;
