<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>منصة الدورات التدريبية الإلكترونية</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Feather Icons -->
    <script src="https://unpkg.com/feather-icons"></script>
    
    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore-compat.js"></script>
    
    <!-- Supabase SDK -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    
    <!-- Chart.js for analytics -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="src/css/main.css">
    
    <!-- Tailwind Config -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 font-sans">
    <!-- Loading Spinner -->
    <div id="loading-spinner" class="fixed inset-0 bg-white bg-opacity-90 flex items-center justify-center z-50 hidden">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
    </div>

    <!-- Navigation -->
    <nav id="navbar" class="bg-white shadow-sm border-b border-gray-200 hidden">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <h1 class="text-xl font-bold text-primary-600">منصة الدورات التدريبية</h1>
                    </div>
                    <div class="hidden md:ml-6 md:flex md:space-x-8">
                        <a href="#" id="nav-dashboard" class="text-gray-900 hover:text-primary-600 px-3 py-2 text-sm font-medium">Dashboard</a>
                        <a href="#" id="nav-courses" class="text-gray-500 hover:text-primary-600 px-3 py-2 text-sm font-medium">Courses</a>
                        <a href="#" id="nav-quizzes" class="text-gray-500 hover:text-primary-600 px-3 py-2 text-sm font-medium">Quizzes</a>
                        <a href="#" id="nav-students" class="text-gray-500 hover:text-primary-600 px-3 py-2 text-sm font-medium hidden">Students</a>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="relative">
                        <button id="user-menu-button" class="flex items-center space-x-2 text-gray-700 hover:text-primary-600">
                            <img id="user-avatar" class="h-8 w-8 rounded-full" src="https://via.placeholder.com/32" alt="User avatar">
                            <span id="user-name" class="text-sm font-medium">User</span>
                            <i data-feather="chevron-down" class="h-4 w-4"></i>
                        </button>
                        <div id="user-menu" class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10 hidden">
                            <a href="#" id="menu-profile" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Profile</a>
                            <a href="#" id="menu-settings" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Settings</a>
                            <hr class="my-1">
                            <a href="#" id="menu-logout" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Sign out</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main id="main-content" class="min-h-screen">
        <!-- Auth Container -->
        <div id="auth-container" class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
            <div class="max-w-md w-full space-y-8">
                <div>
                    <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                        مرحباً بك في منصة الدورات التدريبية
                    </h2>
                    <p class="mt-2 text-center text-sm text-gray-600">
                        سجل دخولك أو أنشئ حساباً جديداً
                    </p>
                </div>
                
                <!-- Login Form -->
                <form id="login-form" class="mt-8 space-y-6">
                    <div class="rounded-md shadow-sm -space-y-px">
                        <div>
                            <label for="login-email" class="sr-only">البريد الإلكتروني</label>
                            <input id="login-email" name="email" type="email" required
                                   class="relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm"
                                   placeholder="البريد الإلكتروني">
                        </div>
                        <div>
                            <label for="login-password" class="sr-only">كلمة المرور</label>
                            <input id="login-password" name="password" type="password" required
                                   class="relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm"
                                   placeholder="كلمة المرور">
                        </div>
                    </div>

                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <input id="remember-me" name="remember-me" type="checkbox" 
                                   class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded">
                            <label for="remember-me" class="ml-2 block text-sm text-gray-900">
                                تذكرني
                            </label>
                        </div>

                        <div class="text-sm">
                            <a href="#" id="forgot-password-link" class="font-medium text-primary-600 hover:text-primary-500">
                                نسيت كلمة المرور؟
                            </a>
                        </div>
                    </div>

                    <div>
                        <button type="submit" 
                                class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                            <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                                <i data-feather="lock" class="h-5 w-5 text-primary-500 group-hover:text-primary-400"></i>
                            </span>
                            تسجيل الدخول
                        </button>
                    </div>

                    <div class="text-center">
                        <span class="text-sm text-gray-600">ليس لديك حساب؟ </span>
                        <a href="#" id="show-register" class="font-medium text-primary-600 hover:text-primary-500">إنشاء حساب</a>
                    </div>
                </form>

                <!-- Register Form -->
                <form id="register-form" class="mt-8 space-y-6 hidden">
                    <div class="space-y-4">
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label for="register-firstname" class="block text-sm font-medium text-gray-700">First Name</label>
                                <input id="register-firstname" name="firstName" type="text" required 
                                       class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                            </div>
                            <div>
                                <label for="register-lastname" class="block text-sm font-medium text-gray-700">Last Name</label>
                                <input id="register-lastname" name="lastName" type="text" required 
                                       class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                            </div>
                        </div>
                        <div>
                            <label for="register-email" class="block text-sm font-medium text-gray-700">Email</label>
                            <input id="register-email" name="email" type="email" required 
                                   class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                        </div>
                        <div>
                            <label for="register-password" class="block text-sm font-medium text-gray-700">Password</label>
                            <input id="register-password" name="password" type="password" required 
                                   class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                        </div>
                        <div>
                            <label for="register-role" class="block text-sm font-medium text-gray-700">Role</label>
                            <select id="register-role" name="role" 
                                    class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                                <option value="student">Student</option>
                                <option value="instructor">Instructor</option>
                            </select>
                        </div>
                    </div>

                    <div>
                        <button type="submit" 
                                class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                            <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                                <i data-feather="user-plus" class="h-5 w-5 text-primary-500 group-hover:text-primary-400"></i>
                            </span>
                            Create Account
                        </button>
                    </div>

                    <div class="text-center">
                        <span class="text-sm text-gray-600">Already have an account? </span>
                        <a href="#" id="show-login" class="font-medium text-primary-600 hover:text-primary-500">Sign in</a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Dashboard Container -->
        <div id="dashboard-container" class="hidden">
            <!-- Dashboard content will be loaded here -->
        </div>
    </main>

    <!-- Toast Notifications -->
    <div id="toast-container" class="fixed top-4 right-4 z-50 space-y-2"></div>

    <!-- Modals Container -->
    <div id="modals-container"></div>

    <!-- Scripts -->
    <script src="src/js/config.js"></script>
    <script src="src/js/auth.js"></script>
    <script src="src/js/api.js"></script>
    <script src="src/js/dashboard.js"></script>
    <script src="src/js/courses.js"></script>
    <script src="src/js/videos.js"></script>
    <script src="src/js/quizzes.js"></script>
    <script src="src/js/utils.js"></script>
    <script src="src/js/main.js"></script>

    <script>
        // Initialize Feather icons
        feather.replace();
    </script>
</body>
</html>
