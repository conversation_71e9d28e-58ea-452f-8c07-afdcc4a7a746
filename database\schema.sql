-- إنشاء قاعدة بيانات منصة الدورات التدريبية
-- هذا الملف يحتوي على جميع الجداول والعلاقات المطلوبة

-- تفعيل الامتدادات المطلوبة
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- جدول المستخدمين
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    firebase_uid VARCHAR(255) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    role VARCHAR(20) NOT NULL DEFAULT 'student' CHECK (role IN ('student', 'instructor', 'admin')),
    phone VARCHAR(20),
    bio TEXT,
    avatar_url TEXT,
    is_active BOOLEAN DEFAULT true,
    email_verified BOOLEAN DEFAULT false,
    last_login_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول الدورات
CREATE TABLE courses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    instructor_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100),
    difficulty_level VARCHAR(20) DEFAULT 'beginner' CHECK (difficulty_level IN ('beginner', 'intermediate', 'advanced')),
    estimated_duration INTEGER, -- بالدقائق
    price DECIMAL(10,2) DEFAULT 0.00,
    thumbnail_url TEXT,
    is_published BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول أقسام الدورة
CREATE TABLE course_sections (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    course_id UUID NOT NULL REFERENCES courses(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    order_index INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(course_id, order_index)
);

-- جدول الفيديوهات
CREATE TABLE videos (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    section_id UUID NOT NULL REFERENCES course_sections(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    video_url TEXT NOT NULL,
    thumbnail_url TEXT,
    duration INTEGER, -- بالثواني
    order_index INTEGER NOT NULL,
    is_preview BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(section_id, order_index)
);

-- جدول ملفات PDF
CREATE TABLE pdfs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    video_id UUID NOT NULL REFERENCES videos(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    pdf_url TEXT NOT NULL,
    file_size BIGINT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول الاختبارات
CREATE TABLE quizzes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    course_id UUID NOT NULL REFERENCES courses(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    questions JSONB NOT NULL,
    passing_score INTEGER DEFAULT 70 CHECK (passing_score >= 0 AND passing_score <= 100),
    time_limit INTEGER, -- بالدقائق
    max_attempts INTEGER DEFAULT 3,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول التسجيل في الدورات
CREATE TABLE course_enrollments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    course_id UUID NOT NULL REFERENCES courses(id) ON DELETE CASCADE,
    student_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    enrolled_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    progress_percentage INTEGER DEFAULT 0 CHECK (progress_percentage >= 0 AND progress_percentage <= 100),
    last_accessed_at TIMESTAMP WITH TIME ZONE,
    UNIQUE(course_id, student_id)
);

-- جدول تقدم مشاهدة الفيديوهات
CREATE TABLE video_progress (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    video_id UUID NOT NULL REFERENCES videos(id) ON DELETE CASCADE,
    student_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    watched_duration INTEGER DEFAULT 0, -- بالثواني
    is_completed BOOLEAN DEFAULT false,
    last_watched_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(video_id, student_id)
);

-- جدول محاولات الاختبارات
CREATE TABLE quiz_attempts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    quiz_id UUID NOT NULL REFERENCES quizzes(id) ON DELETE CASCADE,
    student_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    answers JSONB NOT NULL,
    score INTEGER NOT NULL CHECK (score >= 0 AND score <= 100),
    passed BOOLEAN NOT NULL,
    time_taken INTEGER, -- بالثواني
    completed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول الشهادات
CREATE TABLE certificates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    course_id UUID NOT NULL REFERENCES courses(id) ON DELETE CASCADE,
    student_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    certificate_url TEXT,
    issued_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(course_id, student_id)
);

-- جدول التقييمات والمراجعات
CREATE TABLE course_reviews (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    course_id UUID NOT NULL REFERENCES courses(id) ON DELETE CASCADE,
    student_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    review_text TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(course_id, student_id)
);

-- جدول الإشعارات
CREATE TABLE notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    type VARCHAR(50) NOT NULL,
    is_read BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء الفهارس لتحسين الأداء
CREATE INDEX idx_users_firebase_uid ON users(firebase_uid);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);

CREATE INDEX idx_courses_instructor_id ON courses(instructor_id);
CREATE INDEX idx_courses_category ON courses(category);
CREATE INDEX idx_courses_is_published ON courses(is_published);

CREATE INDEX idx_course_sections_course_id ON course_sections(course_id);
CREATE INDEX idx_course_sections_order ON course_sections(course_id, order_index);

CREATE INDEX idx_videos_section_id ON videos(section_id);
CREATE INDEX idx_videos_order ON videos(section_id, order_index);

CREATE INDEX idx_pdfs_video_id ON pdfs(video_id);

CREATE INDEX idx_quizzes_course_id ON quizzes(course_id);
CREATE INDEX idx_quizzes_is_active ON quizzes(is_active);

CREATE INDEX idx_enrollments_course_id ON course_enrollments(course_id);
CREATE INDEX idx_enrollments_student_id ON course_enrollments(student_id);
CREATE INDEX idx_enrollments_progress ON course_enrollments(student_id, progress_percentage);

CREATE INDEX idx_video_progress_video_id ON video_progress(video_id);
CREATE INDEX idx_video_progress_student_id ON video_progress(student_id);

CREATE INDEX idx_quiz_attempts_quiz_id ON quiz_attempts(quiz_id);
CREATE INDEX idx_quiz_attempts_student_id ON quiz_attempts(student_id);

CREATE INDEX idx_certificates_course_id ON certificates(course_id);
CREATE INDEX idx_certificates_student_id ON certificates(student_id);

CREATE INDEX idx_reviews_course_id ON course_reviews(course_id);
CREATE INDEX idx_reviews_student_id ON course_reviews(student_id);

CREATE INDEX idx_notifications_user_id ON notifications(user_id);
CREATE INDEX idx_notifications_is_read ON notifications(user_id, is_read);

-- إنشاء دوال لتحديث updated_at تلقائياً
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- إضافة المشغلات لتحديث updated_at
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_courses_updated_at BEFORE UPDATE ON courses
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_course_sections_updated_at BEFORE UPDATE ON course_sections
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_videos_updated_at BEFORE UPDATE ON videos
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_quizzes_updated_at BEFORE UPDATE ON quizzes
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_course_reviews_updated_at BEFORE UPDATE ON course_reviews
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- إنشاء دالة للبحث في الدورات
CREATE OR REPLACE FUNCTION search_courses(search_term TEXT)
RETURNS TABLE (
    id UUID,
    title VARCHAR(255),
    description TEXT,
    category VARCHAR(100),
    instructor_name TEXT,
    rating DECIMAL(3,2)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        c.id,
        c.title,
        c.description,
        c.category,
        CONCAT(u.first_name, ' ', u.last_name) as instructor_name,
        COALESCE(AVG(cr.rating), 0)::DECIMAL(3,2) as rating
    FROM courses c
    JOIN users u ON c.instructor_id = u.id
    LEFT JOIN course_reviews cr ON c.id = cr.course_id
    WHERE 
        c.is_published = true AND
        (
            c.title ILIKE '%' || search_term || '%' OR
            c.description ILIKE '%' || search_term || '%' OR
            c.category ILIKE '%' || search_term || '%' OR
            CONCAT(u.first_name, ' ', u.last_name) ILIKE '%' || search_term || '%'
        )
    GROUP BY c.id, c.title, c.description, c.category, u.first_name, u.last_name
    ORDER BY rating DESC, c.created_at DESC;
END;
$$ LANGUAGE plpgsql;

-- إنشاء دالة لحساب تقدم الطالب في الدورة
CREATE OR REPLACE FUNCTION calculate_course_progress(p_course_id UUID, p_student_id UUID)
RETURNS INTEGER AS $$
DECLARE
    total_videos INTEGER;
    completed_videos INTEGER;
    progress_percentage INTEGER;
BEGIN
    -- حساب إجمالي الفيديوهات في الدورة
    SELECT COUNT(v.id) INTO total_videos
    FROM videos v
    JOIN course_sections cs ON v.section_id = cs.id
    WHERE cs.course_id = p_course_id;
    
    -- حساب الفيديوهات المكتملة
    SELECT COUNT(vp.id) INTO completed_videos
    FROM video_progress vp
    JOIN videos v ON vp.video_id = v.id
    JOIN course_sections cs ON v.section_id = cs.id
    WHERE cs.course_id = p_course_id 
    AND vp.student_id = p_student_id 
    AND vp.is_completed = true;
    
    -- حساب النسبة المئوية
    IF total_videos > 0 THEN
        progress_percentage := ROUND((completed_videos::DECIMAL / total_videos::DECIMAL) * 100);
    ELSE
        progress_percentage := 0;
    END IF;
    
    -- تحديث جدول التسجيل
    UPDATE course_enrollments 
    SET progress_percentage = progress_percentage,
        last_accessed_at = NOW(),
        completed_at = CASE WHEN progress_percentage = 100 THEN NOW() ELSE completed_at END
    WHERE course_id = p_course_id AND student_id = p_student_id;
    
    RETURN progress_percentage;
END;
$$ LANGUAGE plpgsql;

-- إنشاء مشغل لتحديث تقدم الدورة عند إكمال فيديو
CREATE OR REPLACE FUNCTION update_course_progress_trigger()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.is_completed = true AND (OLD.is_completed IS NULL OR OLD.is_completed = false) THEN
        PERFORM calculate_course_progress(
            (SELECT cs.course_id FROM course_sections cs 
             JOIN videos v ON cs.id = v.section_id 
             WHERE v.id = NEW.video_id),
            NEW.student_id
        );
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_course_progress
    AFTER INSERT OR UPDATE ON video_progress
    FOR EACH ROW EXECUTE FUNCTION update_course_progress_trigger();

-- إدراج بيانات أولية
INSERT INTO users (firebase_uid, email, first_name, last_name, role, is_active) VALUES
('admin-uid-123', '<EMAIL>', 'مدير', 'النظام', 'admin', true),
('instructor-uid-456', '<EMAIL>', 'أحمد', 'محمد', 'instructor', true),
('student-uid-789', '<EMAIL>', 'فاطمة', 'علي', 'student', true);

-- إدراج دورة تجريبية
INSERT INTO courses (instructor_id, title, description, category, difficulty_level, estimated_duration, is_published) VALUES
((SELECT id FROM users WHERE email = '<EMAIL>'), 
 'مقدمة في البرمجة', 
 'تعلم أساسيات البرمجة من الصفر باستخدام JavaScript', 
 'programming', 
 'beginner', 
 480, 
 true);

COMMIT;
