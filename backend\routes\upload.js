const express = require('express');
const { body, param, query } = require('express-validator');
const UploadController = require('../controllers/uploadController');
const {
  verifyFirebaseToken,
  requireAuthenticated,
  requireInstructorOrAdmin
} = require('../middleware/auth');

const router = express.Router();

// Validation rules
const deleteFileValidation = [
  body('bucket')
    .notEmpty()
    .isIn(['videos', 'pdfs', 'avatars', 'certificates'])
    .withMessage('Invalid bucket name'),
  body('filePath')
    .notEmpty()
    .withMessage('File path is required')
];

const listFilesValidation = [
  query('bucket')
    .notEmpty()
    .isIn(['videos', 'pdfs', 'avatars', 'certificates'])
    .withMessage('Invalid bucket name'),
  query('folder')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Folder name too long')
];

const fileInfoValidation = [
  param('bucket')
    .isIn(['videos', 'pdfs', 'avatars', 'certificates'])
    .withMessage('Invalid bucket name'),
  param('filePath')
    .notEmpty()
    .withMessage('File path is required')
];

// Configure multer for different file types
const videoUpload = UploadController.getMulterConfig('video').single('video');
const pdfUpload = UploadController.getMulterConfig('pdf').single('pdf');
const imageUpload = UploadController.getMulterConfig('image').single('image');

// Routes

/**
 * @route   POST /api/upload/video
 * @desc    Upload video file
 * @access  Private (Instructor/Admin)
 */
router.post('/video',
  verifyFirebaseToken,
  requireInstructorOrAdmin,
  (req, res, next) => {
    videoUpload(req, res, (err) => {
      if (err) {
        if (err.code === 'LIMIT_FILE_SIZE') {
          return res.status(400).json({
            success: false,
            message: 'Video file is too large. Maximum size is 500MB.'
          });
        }
        return res.status(400).json({
          success: false,
          message: err.message
        });
      }
      next();
    });
  },
  UploadController.uploadVideo
);

/**
 * @route   POST /api/upload/pdf
 * @desc    Upload PDF file
 * @access  Private (Instructor/Admin)
 */
router.post('/pdf',
  verifyFirebaseToken,
  requireInstructorOrAdmin,
  (req, res, next) => {
    pdfUpload(req, res, (err) => {
      if (err) {
        if (err.code === 'LIMIT_FILE_SIZE') {
          return res.status(400).json({
            success: false,
            message: 'PDF file is too large. Maximum size is 50MB.'
          });
        }
        return res.status(400).json({
          success: false,
          message: err.message
        });
      }
      next();
    });
  },
  UploadController.uploadPdf
);

/**
 * @route   POST /api/upload/image
 * @desc    Upload image file (avatar, thumbnail, etc.)
 * @access  Private (Authenticated users)
 */
router.post('/image',
  verifyFirebaseToken,
  requireAuthenticated,
  (req, res, next) => {
    imageUpload(req, res, (err) => {
      if (err) {
        if (err.code === 'LIMIT_FILE_SIZE') {
          return res.status(400).json({
            success: false,
            message: 'Image file is too large. Maximum size is 50MB.'
          });
        }
        return res.status(400).json({
          success: false,
          message: err.message
        });
      }
      next();
    });
  },
  UploadController.uploadImage
);

/**
 * @route   DELETE /api/upload/file
 * @desc    Delete file from storage
 * @access  Private (File owner/Admin)
 */
router.delete('/file',
  verifyFirebaseToken,
  requireAuthenticated,
  deleteFileValidation,
  UploadController.deleteFile
);

/**
 * @route   GET /api/upload/files
 * @desc    List user's files
 * @access  Private (Authenticated users)
 */
router.get('/files',
  verifyFirebaseToken,
  requireAuthenticated,
  listFilesValidation,
  UploadController.listFiles
);

/**
 * @route   GET /api/upload/:bucket/:filePath
 * @desc    Get file info and URL
 * @access  Private (File owner/Admin)
 */
router.get('/:bucket/*',
  verifyFirebaseToken,
  requireAuthenticated,
  (req, res, next) => {
    // Extract file path from the remaining URL
    req.params.filePath = req.params[0];
    next();
  },
  fileInfoValidation,
  UploadController.getFileInfo
);

// Health check endpoint
router.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'Upload service is running',
    timestamp: new Date().toISOString(),
    limits: {
      maxVideoSize: process.env.MAX_VIDEO_SIZE || '500MB',
      maxPdfSize: process.env.MAX_PDF_SIZE || '50MB'
    }
  });
});

module.exports = router;
