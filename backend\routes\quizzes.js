const express = require('express');
const { body, param, query } = require('express-validator');
const Quiz = require('../models/Quiz');
const {
  verifyFirebaseToken,
  requireAuthenticated,
  requireInstructorOrAdmin,
  optionalAuth
} = require('../middleware/auth');

const router = express.Router();

// Validation rules
const createQuizValidation = [
  body('courseId')
    .isUUID()
    .withMessage('Course ID must be a valid UUID'),
  body('title')
    .trim()
    .isLength({ min: 3, max: 255 })
    .withMessage('Title must be between 3 and 255 characters'),
  body('description')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('Description must not exceed 1000 characters'),
  body('questions')
    .isArray({ min: 1 })
    .withMessage('Questions must be a non-empty array'),
  body('questions.*.type')
    .isIn(['multiple_choice', 'true_false', 'short_answer'])
    .withMessage('Question type must be multiple_choice, true_false, or short_answer'),
  body('questions.*.question')
    .trim()
    .isLength({ min: 5 })
    .withMessage('Question text must be at least 5 characters'),
  body('questions.*.correctAnswer')
    .notEmpty()
    .withMessage('Correct answer is required'),
  body('passingScore')
    .optional()
    .isInt({ min: 0, max: 100 })
    .withMessage('Passing score must be between 0 and 100'),
  body('timeLimit')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Time limit must be a positive integer (minutes)'),
  body('maxAttempts')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Max attempts must be a positive integer')
];

const updateQuizValidation = [
  body('title')
    .optional()
    .trim()
    .isLength({ min: 3, max: 255 })
    .withMessage('Title must be between 3 and 255 characters'),
  body('description')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('Description must not exceed 1000 characters'),
  body('questions')
    .optional()
    .isArray({ min: 1 })
    .withMessage('Questions must be a non-empty array'),
  body('passingScore')
    .optional()
    .isInt({ min: 0, max: 100 })
    .withMessage('Passing score must be between 0 and 100'),
  body('timeLimit')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Time limit must be a positive integer (minutes)'),
  body('maxAttempts')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Max attempts must be a positive integer'),
  body('isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive must be a boolean')
];

const submitQuizValidation = [
  body('answers')
    .isArray()
    .withMessage('Answers must be an array'),
  body('timeTaken')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Time taken must be a positive integer (seconds)')
];

const idValidation = [
  param('id')
    .isUUID()
    .withMessage('Quiz ID must be a valid UUID')
];

// Routes

/**
 * @route   GET /api/quizzes/course/:courseId
 * @desc    Get all quizzes for a course
 * @access  Public (basic info) / Private (full access for enrolled students)
 */
router.get('/course/:courseId',
  optionalAuth,
  [param('courseId').isUUID().withMessage('Course ID must be a valid UUID')],
  async (req, res) => {
    try {
      const { courseId } = req.params;
      const { includeAttempts = false } = req.query;

      const options = {
        includeAttempts: includeAttempts === 'true' && req.user,
        studentId: req.user?.id
      };

      const quizzes = await Quiz.findAll(courseId, options);

      // Return student version for students, full version for instructors/admins
      const responseQuizzes = quizzes.map(quiz => {
        if (req.user && (req.user.role === 'instructor' || req.user.role === 'admin')) {
          return quiz.toJSON();
        } else {
          return quiz.getStudentVersion();
        }
      });

      res.json({
        success: true,
        data: {
          quizzes: responseQuizzes
        }
      });

    } catch (error) {
      console.error('Get quizzes error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch quizzes'
      });
    }
  }
);

/**
 * @route   GET /api/quizzes/:id
 * @desc    Get quiz by ID
 * @access  Private (Enrolled students/Instructors/Admins)
 */
router.get('/:id',
  verifyFirebaseToken,
  requireAuthenticated,
  idValidation,
  async (req, res) => {
    try {
      const { id } = req.params;
      const { includeAttempts = false } = req.query;

      const options = {
        includeAttempts: includeAttempts === 'true',
        studentId: req.user.id
      };

      const quiz = await Quiz.findById(id, options);

      if (!quiz) {
        return res.status(404).json({
          success: false,
          message: 'Quiz not found'
        });
      }

      // Return appropriate version based on user role
      let responseQuiz;
      if (req.user.role === 'instructor' || req.user.role === 'admin') {
        responseQuiz = quiz.toJSON();
      } else {
        responseQuiz = quiz.getStudentVersion();
      }

      res.json({
        success: true,
        data: {
          quiz: responseQuiz
        }
      });

    } catch (error) {
      console.error('Get quiz error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch quiz'
      });
    }
  }
);

/**
 * @route   POST /api/quizzes
 * @desc    Create new quiz
 * @access  Private (Instructor/Admin)
 */
router.post('/',
  verifyFirebaseToken,
  requireInstructorOrAdmin,
  createQuizValidation,
  async (req, res) => {
    try {
      const quiz = await Quiz.create(req.body);

      res.status(201).json({
        success: true,
        message: 'Quiz created successfully',
        data: {
          quiz: quiz.toJSON()
        }
      });

    } catch (error) {
      console.error('Create quiz error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to create quiz'
      });
    }
  }
);

/**
 * @route   PUT /api/quizzes/:id
 * @desc    Update quiz
 * @access  Private (Course owner/Admin)
 */
router.put('/:id',
  verifyFirebaseToken,
  requireInstructorOrAdmin,
  idValidation,
  updateQuizValidation,
  async (req, res) => {
    try {
      const { id } = req.params;
      const quiz = await Quiz.findById(id);

      if (!quiz) {
        return res.status(404).json({
          success: false,
          message: 'Quiz not found'
        });
      }

      await quiz.update(req.body);

      res.json({
        success: true,
        message: 'Quiz updated successfully',
        data: {
          quiz: quiz.toJSON()
        }
      });

    } catch (error) {
      console.error('Update quiz error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update quiz'
      });
    }
  }
);

/**
 * @route   DELETE /api/quizzes/:id
 * @desc    Delete quiz (soft delete)
 * @access  Private (Course owner/Admin)
 */
router.delete('/:id',
  verifyFirebaseToken,
  requireInstructorOrAdmin,
  idValidation,
  async (req, res) => {
    try {
      const { id } = req.params;
      const quiz = await Quiz.findById(id);

      if (!quiz) {
        return res.status(404).json({
          success: false,
          message: 'Quiz not found'
        });
      }

      await quiz.delete();

      res.json({
        success: true,
        message: 'Quiz deleted successfully'
      });

    } catch (error) {
      console.error('Delete quiz error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to delete quiz'
      });
    }
  }
);

/**
 * @route   POST /api/quizzes/:id/submit
 * @desc    Submit quiz attempt
 * @access  Private (Student)
 */
router.post('/:id/submit',
  verifyFirebaseToken,
  requireAuthenticated,
  idValidation,
  submitQuizValidation,
  async (req, res) => {
    try {
      const { id } = req.params;
      const { answers, timeTaken } = req.body;
      const studentId = req.user.id;

      const quiz = await Quiz.findById(id);

      if (!quiz) {
        return res.status(404).json({
          success: false,
          message: 'Quiz not found'
        });
      }

      if (!quiz.isActive) {
        return res.status(400).json({
          success: false,
          message: 'Quiz is not active'
        });
      }

      // Check if student can attempt
      const canAttempt = await quiz.canStudentAttempt(studentId);
      if (!canAttempt) {
        return res.status(400).json({
          success: false,
          message: 'Maximum attempts exceeded'
        });
      }

      const attempt = await quiz.submitAttempt(studentId, answers, timeTaken);

      res.json({
        success: true,
        message: 'Quiz submitted successfully',
        data: {
          attempt: {
            id: attempt.id,
            score: attempt.score,
            passed: attempt.passed,
            timeTaken: attempt.time_taken,
            completedAt: attempt.completed_at
          }
        }
      });

    } catch (error) {
      console.error('Submit quiz error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to submit quiz'
      });
    }
  }
);

/**
 * @route   GET /api/quizzes/:id/attempts
 * @desc    Get quiz attempts
 * @access  Private (Course owner for all attempts, Student for own attempts)
 */
router.get('/:id/attempts',
  verifyFirebaseToken,
  requireAuthenticated,
  idValidation,
  async (req, res) => {
    try {
      const { id } = req.params;
      const quiz = await Quiz.findById(id);

      if (!quiz) {
        return res.status(404).json({
          success: false,
          message: 'Quiz not found'
        });
      }

      let attempts;
      if (req.user.role === 'instructor' || req.user.role === 'admin') {
        // Instructors/admins can see all attempts
        attempts = await quiz.getAttempts();
      } else {
        // Students can only see their own attempts
        attempts = await quiz.getAttempts(req.user.id);
      }

      res.json({
        success: true,
        data: {
          attempts
        }
      });

    } catch (error) {
      console.error('Get attempts error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch attempts'
      });
    }
  }
);

/**
 * @route   GET /api/quizzes/:id/statistics
 * @desc    Get quiz statistics
 * @access  Private (Course owner/Admin)
 */
router.get('/:id/statistics',
  verifyFirebaseToken,
  requireInstructorOrAdmin,
  idValidation,
  async (req, res) => {
    try {
      const { id } = req.params;
      const quiz = await Quiz.findById(id);

      if (!quiz) {
        return res.status(404).json({
          success: false,
          message: 'Quiz not found'
        });
      }

      const statistics = await quiz.getStatistics();

      res.json({
        success: true,
        data: {
          statistics
        }
      });

    } catch (error) {
      console.error('Get statistics error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch statistics'
      });
    }
  }
);

module.exports = router;
