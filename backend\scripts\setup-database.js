const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function setupDatabase() {
  console.log('🚀 Setting up database...');

  try {
    // Create users table
    const { error: usersError } = await supabase.rpc('create_users_table');
    if (usersError && !usersError.message.includes('already exists')) {
      console.error('Error creating users table:', usersError);
    } else {
      console.log('✅ Users table created/verified');
    }

    // Create courses table
    const { error: coursesError } = await supabase.rpc('create_courses_table');
    if (coursesError && !coursesError.message.includes('already exists')) {
      console.error('Error creating courses table:', coursesError);
    } else {
      console.log('✅ Courses table created/verified');
    }

    // Create course_sections table
    const { error: sectionsError } = await supabase.rpc('create_course_sections_table');
    if (sectionsError && !sectionsError.message.includes('already exists')) {
      console.error('Error creating course_sections table:', sectionsError);
    } else {
      console.log('✅ Course sections table created/verified');
    }

    // Create videos table
    const { error: videosError } = await supabase.rpc('create_videos_table');
    if (videosError && !videosError.message.includes('already exists')) {
      console.error('Error creating videos table:', videosError);
    } else {
      console.log('✅ Videos table created/verified');
    }

    // Create pdfs table
    const { error: pdfsError } = await supabase.rpc('create_pdfs_table');
    if (pdfsError && !pdfsError.message.includes('already exists')) {
      console.error('Error creating pdfs table:', pdfsError);
    } else {
      console.log('✅ PDFs table created/verified');
    }

    // Create quizzes table
    const { error: quizzesError } = await supabase.rpc('create_quizzes_table');
    if (quizzesError && !quizzesError.message.includes('already exists')) {
      console.error('Error creating quizzes table:', quizzesError);
    } else {
      console.log('✅ Quizzes table created/verified');
    }

    // Create quiz_attempts table
    const { error: attemptsError } = await supabase.rpc('create_quiz_attempts_table');
    if (attemptsError && !attemptsError.message.includes('already exists')) {
      console.error('Error creating quiz_attempts table:', attemptsError);
    } else {
      console.log('✅ Quiz attempts table created/verified');
    }

    // Create certificates table
    const { error: certificatesError } = await supabase.rpc('create_certificates_table');
    if (certificatesError && !certificatesError.message.includes('already exists')) {
      console.error('Error creating certificates table:', certificatesError);
    } else {
      console.log('✅ Certificates table created/verified');
    }

    // Create course_enrollments table
    const { error: enrollmentsError } = await supabase.rpc('create_course_enrollments_table');
    if (enrollmentsError && !enrollmentsError.message.includes('already exists')) {
      console.error('Error creating course_enrollments table:', enrollmentsError);
    } else {
      console.log('✅ Course enrollments table created/verified');
    }

    // Setup storage buckets
    await setupStorageBuckets();

    console.log('🎉 Database setup completed successfully!');
  } catch (error) {
    console.error('❌ Database setup failed:', error);
    process.exit(1);
  }
}

async function setupStorageBuckets() {
  console.log('📁 Setting up storage buckets...');

  const buckets = [
    { name: 'videos', public: false },
    { name: 'pdfs', public: false },
    { name: 'certificates', public: false },
    { name: 'avatars', public: true }
  ];

  for (const bucket of buckets) {
    const { error } = await supabase.storage.createBucket(bucket.name, {
      public: bucket.public,
      fileSizeLimit: bucket.name === 'videos' ? 524288000 : 52428800 // 500MB for videos, 50MB for others
    });

    if (error && !error.message.includes('already exists')) {
      console.error(`Error creating ${bucket.name} bucket:`, error);
    } else {
      console.log(`✅ ${bucket.name} bucket created/verified`);
    }
  }
}

if (require.main === module) {
  setupDatabase();
}

module.exports = { setupDatabase };
