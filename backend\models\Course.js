const { supabase } = require('../config/database');

class Course {
  constructor(data) {
    this.id = data.id;
    this.title = data.title;
    this.description = data.description;
    this.thumbnailUrl = data.thumbnail_url;
    this.instructorId = data.instructor_id;
    this.category = data.category;
    this.difficultyLevel = data.difficulty_level;
    this.estimatedDuration = data.estimated_duration;
    this.isPublished = data.is_published;
    this.price = data.price;
    this.createdAt = data.created_at;
    this.updatedAt = data.updated_at;
    
    // Additional fields from joins
    this.instructor = data.instructor;
    this.sectionsCount = data.sections_count;
    this.videosCount = data.videos_count;
    this.enrollmentsCount = data.enrollments_count;
  }

  static async findAll(options = {}) {
    const { 
      limit = 50, 
      offset = 0, 
      instructorId = null, 
      published = null,
      category = null 
    } = options;

    let query = supabase
      .from('courses')
      .select(`
        *,
        instructor:users!courses_instructor_id_fkey(id, first_name, last_name, avatar_url),
        sections_count:course_sections(count),
        enrollments_count:course_enrollments(count)
      `);

    if (instructorId) {
      query = query.eq('instructor_id', instructorId);
    }

    if (published !== null) {
      query = query.eq('is_published', published);
    }

    if (category) {
      query = query.eq('category', category);
    }

    const { data, error } = await query
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) throw error;

    return data.map(course => new Course(course));
  }

  static async findById(id) {
    const { data, error } = await supabase
      .from('courses')
      .select(`
        *,
        instructor:users!courses_instructor_id_fkey(id, first_name, last_name, avatar_url, bio),
        sections:course_sections(
          id, title, description, order_index,
          videos:videos(id, title, description, duration, order_index, is_preview)
        )
      `)
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') return null;
      throw error;
    }

    return new Course(data);
  }

  static async create(courseData) {
    const { data, error } = await supabase
      .from('courses')
      .insert({
        title: courseData.title,
        description: courseData.description,
        thumbnail_url: courseData.thumbnailUrl,
        instructor_id: courseData.instructorId,
        category: courseData.category,
        difficulty_level: courseData.difficultyLevel,
        estimated_duration: courseData.estimatedDuration,
        price: courseData.price || 0,
        is_published: courseData.isPublished || false
      })
      .select()
      .single();

    if (error) throw error;

    return new Course(data);
  }

  async update(updateData) {
    const { data, error } = await supabase
      .from('courses')
      .update({
        title: updateData.title || this.title,
        description: updateData.description || this.description,
        thumbnail_url: updateData.thumbnailUrl || this.thumbnailUrl,
        category: updateData.category || this.category,
        difficulty_level: updateData.difficultyLevel || this.difficultyLevel,
        estimated_duration: updateData.estimatedDuration || this.estimatedDuration,
        price: updateData.price !== undefined ? updateData.price : this.price,
        is_published: updateData.isPublished !== undefined ? updateData.isPublished : this.isPublished
      })
      .eq('id', this.id)
      .select()
      .single();

    if (error) throw error;

    Object.assign(this, new Course(data));
    return this;
  }

  async delete() {
    const { error } = await supabase
      .from('courses')
      .delete()
      .eq('id', this.id);

    if (error) throw error;
    return true;
  }

  static async search(query, options = {}) {
    const { limit = 20, published = true } = options;

    let queryBuilder = supabase
      .from('courses')
      .select(`
        *,
        instructor:users!courses_instructor_id_fkey(id, first_name, last_name, avatar_url)
      `)
      .or(`title.ilike.%${query}%,description.ilike.%${query}%,category.ilike.%${query}%`);

    if (published !== null) {
      queryBuilder = queryBuilder.eq('is_published', published);
    }

    const { data, error } = await queryBuilder
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) throw error;

    return data.map(course => new Course(course));
  }

  async getEnrollments() {
    const { data, error } = await supabase
      .from('course_enrollments')
      .select(`
        *,
        student:users!course_enrollments_student_id_fkey(id, first_name, last_name, email, avatar_url)
      `)
      .eq('course_id', this.id)
      .order('enrolled_at', { ascending: false });

    if (error) throw error;
    return data;
  }

  async enrollStudent(studentId) {
    const { data, error } = await supabase
      .from('course_enrollments')
      .insert({
        course_id: this.id,
        student_id: studentId
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async unenrollStudent(studentId) {
    const { error } = await supabase
      .from('course_enrollments')
      .delete()
      .eq('course_id', this.id)
      .eq('student_id', studentId);

    if (error) throw error;
    return true;
  }

  async getProgress(studentId) {
    const { data, error } = await supabase
      .from('course_enrollments')
      .select('progress_percentage, last_accessed_video_id, completed_at')
      .eq('course_id', this.id)
      .eq('student_id', studentId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') return null;
      throw error;
    }

    return data;
  }

  async updateProgress(studentId, progressData) {
    const { data, error } = await supabase
      .from('course_enrollments')
      .update({
        progress_percentage: progressData.progressPercentage,
        last_accessed_video_id: progressData.lastAccessedVideoId,
        completed_at: progressData.completedAt
      })
      .eq('course_id', this.id)
      .eq('student_id', studentId)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  toJSON() {
    return {
      id: this.id,
      title: this.title,
      description: this.description,
      thumbnailUrl: this.thumbnailUrl,
      instructorId: this.instructorId,
      instructor: this.instructor,
      category: this.category,
      difficultyLevel: this.difficultyLevel,
      estimatedDuration: this.estimatedDuration,
      isPublished: this.isPublished,
      price: this.price,
      sectionsCount: this.sectionsCount,
      videosCount: this.videosCount,
      enrollmentsCount: this.enrollmentsCount,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    };
  }
}

module.exports = Course;
