{"indexes": [{"collectionGroup": "courseProgress", "queryScope": "COLLECTION", "fields": [{"fieldPath": "studentId", "order": "ASCENDING"}, {"fieldPath": "lastUpdated", "order": "DESCENDING"}]}, {"collectionGroup": "notifications", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "isRead", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "discussions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "courseId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}], "fieldOverrides": []}