@echo off
echo ========================================
echo    منصة الدورات التدريبية الإلكترونية
echo ========================================
echo.

echo جاري التحقق من Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: Node.js غير مثبت على النظام
    echo يرجى تحميل وتثبيت Node.js من: https://nodejs.org/
    pause
    exit /b 1
)

echo Node.js موجود ✓
echo.

echo جاري الانتقال لمجلد الخادم الخلفي...
cd backend

echo جاري التحقق من وجود node_modules...
if not exist "node_modules" (
    echo جاري تثبيت التبعيات...
    npm install
    if %errorlevel% neq 0 (
        echo خطأ في تثبيت التبعيات
        pause
        exit /b 1
    )
) else (
    echo التبعيات موجودة ✓
)

echo.
echo جاري إنشاء ملف .env إذا لم يكن موجوداً...
if not exist ".env" (
    copy ".env.example" ".env"
    echo تم إنشاء ملف .env
    echo يرجى تعديل الإعدادات في ملف .env قبل التشغيل
)

echo.
echo ========================================
echo جاري تشغيل الخادم الخلفي...
echo ========================================
echo الخادم سيعمل على: http://localhost:3000
echo الواجهة الأمامية على: http://localhost:3001
echo.
echo لإيقاف الخادم اضغط Ctrl+C
echo ========================================
echo.

npm start

pause
