@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* Base styles */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Inter', system-ui, -apple-system, sans-serif;
    line-height: 1.6;
    color: #1f2937;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from { 
        opacity: 0;
        transform: translateY(10px);
    }
    to { 
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideDown {
    from { 
        opacity: 0;
        transform: translateY(-10px);
    }
    to { 
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

.animate-slide-up {
    animation: slideUp 0.3s ease-out;
}

.animate-slide-down {
    animation: slideDown 0.3s ease-out;
}

.animate-pulse-slow {
    animation: pulse 3s infinite;
}

/* Custom components */
.btn {
    @apply inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-200;
}

.btn-primary {
    @apply bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
}

.btn-secondary {
    @apply bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500;
}

.btn-danger {
    @apply bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
}

.btn-success {
    @apply bg-green-600 text-white hover:bg-green-700 focus:ring-green-500;
}

.btn-sm {
    @apply px-3 py-1.5 text-xs;
}

.btn-lg {
    @apply px-6 py-3 text-base;
}

.card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden;
}

.card-header {
    @apply px-6 py-4 border-b border-gray-200 bg-gray-50;
}

.card-body {
    @apply px-6 py-4;
}

.card-footer {
    @apply px-6 py-4 border-t border-gray-200 bg-gray-50;
}

/* Form styles */
.form-group {
    @apply mb-4;
}

.form-label {
    @apply block text-sm font-medium text-gray-700 mb-1;
}

.form-input {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm;
}

.form-input:invalid {
    @apply border-red-300 focus:ring-red-500 focus:border-red-500;
}

.form-error {
    @apply mt-1 text-sm text-red-600;
}

.form-help {
    @apply mt-1 text-sm text-gray-500;
}

/* Toast notifications */
.toast {
    @apply max-w-sm w-full bg-white shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden;
    animation: slideDown 0.3s ease-out;
}

.toast-success {
    @apply border-l-4 border-green-400;
}

.toast-error {
    @apply border-l-4 border-red-400;
}

.toast-warning {
    @apply border-l-4 border-yellow-400;
}

.toast-info {
    @apply border-l-4 border-blue-400;
}

/* Video player styles */
.video-player {
    @apply relative bg-black rounded-lg overflow-hidden;
}

.video-controls {
    @apply absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-4;
}

.video-progress {
    @apply w-full h-1 bg-gray-600 rounded-full overflow-hidden cursor-pointer;
}

.video-progress-bar {
    @apply h-full bg-primary-500 transition-all duration-200;
}

/* Course card styles */
.course-card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow duration-200;
}

.course-card:hover {
    transform: translateY(-2px);
}

.course-thumbnail {
    @apply w-full h-48 object-cover;
}

.course-badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.course-badge-beginner {
    @apply bg-green-100 text-green-800;
}

.course-badge-intermediate {
    @apply bg-yellow-100 text-yellow-800;
}

.course-badge-advanced {
    @apply bg-red-100 text-red-800;
}

/* Progress bar */
.progress-bar {
    @apply w-full bg-gray-200 rounded-full h-2 overflow-hidden;
}

.progress-fill {
    @apply h-full bg-primary-500 transition-all duration-300 ease-out;
}

/* Quiz styles */
.quiz-question {
    @apply bg-white border border-gray-200 rounded-lg p-6 mb-4;
}

.quiz-option {
    @apply flex items-center p-3 border border-gray-200 rounded-md cursor-pointer hover:bg-gray-50 transition-colors duration-200;
}

.quiz-option.selected {
    @apply bg-primary-50 border-primary-300;
}

.quiz-option input[type="radio"] {
    @apply sr-only;
}

.quiz-option-indicator {
    @apply w-4 h-4 border-2 border-gray-300 rounded-full mr-3 flex-shrink-0;
}

.quiz-option.selected .quiz-option-indicator {
    @apply border-primary-500 bg-primary-500;
}

.quiz-option.selected .quiz-option-indicator::after {
    content: '';
    @apply block w-2 h-2 bg-white rounded-full m-auto;
}

/* Dashboard grid */
.dashboard-grid {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6;
}

.stat-card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
}

.stat-value {
    @apply text-3xl font-bold text-gray-900;
}

.stat-label {
    @apply text-sm font-medium text-gray-500 uppercase tracking-wide;
}

.stat-change {
    @apply text-sm font-medium;
}

.stat-change.positive {
    @apply text-green-600;
}

.stat-change.negative {
    @apply text-red-600;
}

/* Mobile responsive */
@media (max-width: 768px) {
    .dashboard-grid {
        @apply grid-cols-1 gap-4;
    }
    
    .card-body {
        @apply px-4 py-3;
    }
    
    .btn {
        @apply w-full justify-center;
    }
    
    .course-card {
        @apply mx-2;
    }
}

/* Dark mode support (optional) */
@media (prefers-color-scheme: dark) {
    .dark-mode {
        @apply bg-gray-900 text-white;
    }
    
    .dark-mode .card {
        @apply bg-gray-800 border-gray-700;
    }
    
    .dark-mode .form-input {
        @apply bg-gray-800 border-gray-600 text-white;
    }
}

/* Loading states */
.skeleton {
    @apply bg-gray-200 rounded animate-pulse;
}

.skeleton-text {
    @apply h-4 bg-gray-200 rounded animate-pulse;
}

.skeleton-avatar {
    @apply w-10 h-10 bg-gray-200 rounded-full animate-pulse;
}

/* Utility classes */
.text-truncate {
    @apply truncate;
}

.text-wrap {
    word-wrap: break-word;
}

.shadow-soft {
    box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);
}

.border-dashed {
    border-style: dashed;
}

/* Custom focus styles */
.focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500;
}

/* Transitions */
.transition-all {
    transition: all 0.2s ease-in-out;
}

.transition-colors {
    transition: color 0.2s ease-in-out, background-color 0.2s ease-in-out, border-color 0.2s ease-in-out;
}

.transition-transform {
    transition: transform 0.2s ease-in-out;
}
