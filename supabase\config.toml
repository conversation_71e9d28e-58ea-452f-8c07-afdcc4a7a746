# إعدادات مشروع Supabase المحلي

[api]
enabled = true
port = 54321
schemas = ["public", "graphql_public"]
extra_search_path = ["public", "extensions"]
max_rows = 1000

[auth]
enabled = true
port = 54324
site_url = "http://localhost:3001"
additional_redirect_urls = ["https://localhost:3001"]
jwt_expiry = 3600
refresh_token_rotation_enabled = true
security_update_password_require_reauthentication = true
security_captcha_enabled = false

[auth.email]
enable_signup = true
double_confirm_changes = true
enable_confirmations = false

[auth.sms]
enable_signup = false
enable_confirmations = false

[auth.external.apple]
enabled = false

[auth.external.azure]
enabled = false

[auth.external.bitbucket]
enabled = false

[auth.external.discord]
enabled = false

[auth.external.facebook]
enabled = false

[auth.external.github]
enabled = false

[auth.external.gitlab]
enabled = false

[auth.external.google]
enabled = false

[auth.external.keycloak]
enabled = false

[auth.external.linkedin]
enabled = false

[auth.external.notion]
enabled = false

[auth.external.twitch]
enabled = false

[auth.external.twitter]
enabled = false

[auth.external.slack]
enabled = false

[auth.external.spotify]
enabled = false

[auth.external.workos]
enabled = false

[auth.external.zoom]
enabled = false

[db]
port = 54322
shadow_port = 54320
major_version = 15

[db.pooler]
enabled = false
port = 54329
pool_mode = "transaction"
default_pool_size = 20
max_client_conn = 100

[realtime]
enabled = true
port = 54323
ip_version = "ipv4"

[studio]
enabled = true
port = 54323
api_url = "http://localhost:54321"

[inbucket]
enabled = true
port = 54324
smtp_port = 54325
pop3_port = 54326

[storage]
enabled = true
port = 54321
file_size_limit = "50MiB"
buckets = [
  { name = "avatars", public = true },
  { name = "videos", public = false },
  { name = "pdfs", public = false },
  { name = "certificates", public = false }
]

[analytics]
enabled = false
port = 54327
vector_port = 54328
gcp_project_id = ""
gcp_project_number = ""
gcp_jwt_path = "supabase/gcp.json"

[functions]
enabled = true
verify_jwt = false

[functions.import_map]
enabled = false

[edge_runtime]
enabled = true
ip_version = "ipv4"
policy = "per_worker"
inspector_port = 8083
