const express = require('express');
const { body, param, query } = require('express-validator');
const User = require('../models/User');
const {
  verifyFirebaseToken,
  requireAuthenticated,
  requireAdmin,
  requireInstructorOrAdmin
} = require('../middleware/auth');

const router = express.Router();

// Validation rules
const updateUserValidation = [
  body('firstName')
    .optional()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('First name must be between 2 and 50 characters'),
  body('lastName')
    .optional()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Last name must be between 2 and 50 characters'),
  body('phone')
    .optional()
    .isMobilePhone()
    .withMessage('Please provide a valid phone number'),
  body('bio')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Bio must not exceed 500 characters'),
  body('avatarUrl')
    .optional()
    .isURL()
    .withMessage('Avatar URL must be a valid URL'),
  body('role')
    .optional()
    .isIn(['student', 'instructor', 'admin'])
    .withMessage('Role must be student, instructor, or admin'),
  body('isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive must be a boolean')
];

const searchValidation = [
  query('q')
    .optional()
    .isLength({ min: 2 })
    .withMessage('Search query must be at least 2 characters'),
  query('role')
    .optional()
    .isIn(['student', 'instructor', 'admin'])
    .withMessage('Role must be student, instructor, or admin'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100')
];

const idValidation = [
  param('id')
    .isUUID()
    .withMessage('User ID must be a valid UUID')
];

// Routes

/**
 * @route   GET /api/users
 * @desc    Get all users (with filtering)
 * @access  Private (Admin only)
 */
router.get('/',
  verifyFirebaseToken,
  requireAdmin,
  searchValidation,
  async (req, res) => {
    try {
      const { role, limit = 50, offset = 0 } = req.query;

      let users;
      if (role) {
        users = await User.findByRole(role, parseInt(limit), parseInt(offset));
      } else {
        // Get all users (you might want to implement a findAll method)
        users = await User.findByRole('student', parseInt(limit), parseInt(offset));
        const instructors = await User.findByRole('instructor', parseInt(limit), parseInt(offset));
        const admins = await User.findByRole('admin', parseInt(limit), parseInt(offset));
        users = [...users, ...instructors, ...admins];
      }

      res.json({
        success: true,
        data: {
          users: users.map(user => user.toJSON()),
          pagination: {
            limit: parseInt(limit),
            offset: parseInt(offset),
            total: users.length
          }
        }
      });

    } catch (error) {
      console.error('Get users error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch users'
      });
    }
  }
);

/**
 * @route   GET /api/users/search
 * @desc    Search users
 * @access  Private (Instructor/Admin)
 */
router.get('/search',
  verifyFirebaseToken,
  requireInstructorOrAdmin,
  searchValidation,
  async (req, res) => {
    try {
      const { q: query, role, limit = 20 } = req.query;

      if (!query) {
        return res.status(400).json({
          success: false,
          message: 'Search query is required'
        });
      }

      const users = await User.search(query, role, parseInt(limit));

      res.json({
        success: true,
        data: {
          users: users.map(user => user.toJSON())
        }
      });

    } catch (error) {
      console.error('Search users error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to search users'
      });
    }
  }
);

/**
 * @route   GET /api/users/:id
 * @desc    Get user by ID
 * @access  Private (Admin or own profile)
 */
router.get('/:id',
  verifyFirebaseToken,
  requireAuthenticated,
  idValidation,
  async (req, res) => {
    try {
      const { id } = req.params;

      // Check if user is accessing their own profile or is admin
      if (req.user.id !== id && req.user.role !== 'admin') {
        return res.status(403).json({
          success: false,
          message: 'Access denied. You can only view your own profile.'
        });
      }

      const user = await User.findById(id);

      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      res.json({
        success: true,
        data: {
          user: user.toJSON()
        }
      });

    } catch (error) {
      console.error('Get user error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch user'
      });
    }
  }
);

/**
 * @route   PUT /api/users/:id
 * @desc    Update user
 * @access  Private (Admin or own profile)
 */
router.put('/:id',
  verifyFirebaseToken,
  requireAuthenticated,
  idValidation,
  updateUserValidation,
  async (req, res) => {
    try {
      const { id } = req.params;

      // Check permissions
      const isOwnProfile = req.user.id === id;
      const isAdmin = req.user.role === 'admin';

      if (!isOwnProfile && !isAdmin) {
        return res.status(403).json({
          success: false,
          message: 'Access denied. You can only update your own profile.'
        });
      }

      const user = await User.findById(id);

      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      // Prevent non-admins from changing role or isActive
      const updateData = { ...req.body };
      if (!isAdmin) {
        delete updateData.role;
        delete updateData.isActive;
      }

      await user.update(updateData);

      res.json({
        success: true,
        message: 'User updated successfully',
        data: {
          user: user.toJSON()
        }
      });

    } catch (error) {
      console.error('Update user error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update user'
      });
    }
  }
);

/**
 * @route   DELETE /api/users/:id
 * @desc    Delete user (soft delete)
 * @access  Private (Admin only)
 */
router.delete('/:id',
  verifyFirebaseToken,
  requireAdmin,
  idValidation,
  async (req, res) => {
    try {
      const { id } = req.params;

      // Prevent admin from deleting themselves
      if (req.user.id === id) {
        return res.status(400).json({
          success: false,
          message: 'You cannot delete your own account'
        });
      }

      const user = await User.findById(id);

      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      await user.delete();

      res.json({
        success: true,
        message: 'User deleted successfully'
      });

    } catch (error) {
      console.error('Delete user error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to delete user'
      });
    }
  }
);

/**
 * @route   GET /api/users/role/:role
 * @desc    Get users by role
 * @access  Private (Instructor/Admin)
 */
router.get('/role/:role',
  verifyFirebaseToken,
  requireInstructorOrAdmin,
  [
    param('role')
      .isIn(['student', 'instructor', 'admin'])
      .withMessage('Role must be student, instructor, or admin'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('Limit must be between 1 and 100'),
    query('offset')
      .optional()
      .isInt({ min: 0 })
      .withMessage('Offset must be a non-negative integer')
  ],
  async (req, res) => {
    try {
      const { role } = req.params;
      const { limit = 50, offset = 0 } = req.query;

      const users = await User.findByRole(role, parseInt(limit), parseInt(offset));

      res.json({
        success: true,
        data: {
          users: users.map(user => user.toJSON()),
          pagination: {
            limit: parseInt(limit),
            offset: parseInt(offset),
            total: users.length
          }
        }
      });

    } catch (error) {
      console.error('Get users by role error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch users'
      });
    }
  }
);

/**
 * @route   POST /api/users/:id/activate
 * @desc    Activate user account
 * @access  Private (Admin only)
 */
router.post('/:id/activate',
  verifyFirebaseToken,
  requireAdmin,
  idValidation,
  async (req, res) => {
    try {
      const { id } = req.params;

      const user = await User.findById(id);

      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      await user.update({ isActive: true });

      res.json({
        success: true,
        message: 'User activated successfully',
        data: {
          user: user.toJSON()
        }
      });

    } catch (error) {
      console.error('Activate user error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to activate user'
      });
    }
  }
);

/**
 * @route   POST /api/users/:id/deactivate
 * @desc    Deactivate user account
 * @access  Private (Admin only)
 */
router.post('/:id/deactivate',
  verifyFirebaseToken,
  requireAdmin,
  idValidation,
  async (req, res) => {
    try {
      const { id } = req.params;

      // Prevent admin from deactivating themselves
      if (req.user.id === id) {
        return res.status(400).json({
          success: false,
          message: 'You cannot deactivate your own account'
        });
      }

      const user = await User.findById(id);

      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      await user.update({ isActive: false });

      res.json({
        success: true,
        message: 'User deactivated successfully',
        data: {
          user: user.toJSON()
        }
      });

    } catch (error) {
      console.error('Deactivate user error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to deactivate user'
      });
    }
  }
);

module.exports = router;
