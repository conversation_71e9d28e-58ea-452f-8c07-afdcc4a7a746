version: '3.8'

services:
  # الخادم الخلفي
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: courses-backend
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - PORT=3000
    env_file:
      - ./backend/.env
    volumes:
      - ./backend:/app
      - /app/node_modules
    depends_on:
      - postgres
      - redis
    networks:
      - courses-network
    command: npm run dev

  # الواجهة الأمامية
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: courses-frontend
    restart: unless-stopped
    ports:
      - "3001:80"
    volumes:
      - ./frontend:/usr/share/nginx/html
    networks:
      - courses-network

  # قاعدة البيانات PostgreSQL (للتطوير المحلي)
  postgres:
    image: postgres:15-alpine
    container_name: courses-postgres
    restart: unless-stopped
    ports:
      - "5432:5432"
    environment:
      POSTGRES_DB: courses_platform
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password123
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - courses-network

  # Redis للتخزين المؤقت
  redis:
    image: redis:7-alpine
    container_name: courses-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - courses-network
    command: redis-server --appendonly yes

  # Nginx كخادم ويب وموزع الأحمال
  nginx:
    image: nginx:alpine
    container_name: courses-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - ./frontend:/usr/share/nginx/html
    depends_on:
      - backend
      - frontend
    networks:
      - courses-network

  # MinIO للتخزين المحلي (بديل S3)
  minio:
    image: minio/minio:latest
    container_name: courses-minio
    restart: unless-stopped
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin123
    volumes:
      - minio_data:/data
    networks:
      - courses-network
    command: server /data --console-address ":9001"

  # Elasticsearch للبحث المتقدم
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: courses-elasticsearch
    restart: unless-stopped
    ports:
      - "9200:9200"
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - courses-network

  # Kibana لمراقبة Elasticsearch
  kibana:
    image: docker.elastic.co/kibana/kibana:8.11.0
    container_name: courses-kibana
    restart: unless-stopped
    ports:
      - "5601:5601"
    environment:
      ELASTICSEARCH_HOSTS: http://elasticsearch:9200
    depends_on:
      - elasticsearch
    networks:
      - courses-network

  # Prometheus لمراقبة النظام
  prometheus:
    image: prom/prometheus:latest
    container_name: courses-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - courses-network
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'

  # Grafana لعرض المقاييس
  grafana:
    image: grafana/grafana:latest
    container_name: courses-grafana
    restart: unless-stopped
    ports:
      - "3002:3000"
    environment:
      GF_SECURITY_ADMIN_PASSWORD: admin123
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - prometheus
    networks:
      - courses-network

  # Mailhog لاختبار البريد الإلكتروني
  mailhog:
    image: mailhog/mailhog:latest
    container_name: courses-mailhog
    restart: unless-stopped
    ports:
      - "1025:1025"  # SMTP
      - "8025:8025"  # Web UI
    networks:
      - courses-network

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  minio_data:
    driver: local
  elasticsearch_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  courses-network:
    driver: bridge
