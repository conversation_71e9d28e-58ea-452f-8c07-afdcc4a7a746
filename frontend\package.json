{"name": "courses-platform-frontend", "version": "1.0.0", "description": "Frontend for online courses platform", "main": "index.html", "scripts": {"dev": "live-server --port=3001 --host=localhost --open=/index.html", "build": "npm run build:css && npm run build:js", "build:css": "postcss src/css/main.css -o dist/css/main.css", "build:js": "webpack --mode=production", "watch:css": "postcss src/css/main.css -o dist/css/main.css --watch", "serve": "live-server dist --port=3001"}, "dependencies": {"firebase": "^10.7.1", "chart.js": "^4.4.0", "feather-icons": "^4.29.1"}, "devDependencies": {"live-server": "^1.2.2", "postcss": "^8.4.32", "postcss-cli": "^11.0.0", "autoprefixer": "^10.4.16", "tailwindcss": "^3.3.6", "webpack": "^5.89.0", "webpack-cli": "^5.1.4", "babel-loader": "^9.1.3", "@babel/core": "^7.23.6", "@babel/preset-env": "^7.23.6"}}