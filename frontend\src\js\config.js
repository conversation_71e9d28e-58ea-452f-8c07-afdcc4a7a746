// Configuration file for the frontend application

// Firebase configuration
const firebaseConfig = {
    apiKey: "your-api-key",
    authDomain: "your-project.firebaseapp.com",
    projectId: "your-project-id",
    storageBucket: "your-project.appspot.com",
    messagingSenderId: "your-sender-id",
    appId: "your-app-id"
};

// Supabase configuration
const supabaseConfig = {
    url: "https://your-project.supabase.co",
    anonKey: "your-supabase-anon-key"
};

// API configuration
const apiConfig = {
    baseURL: window.location.hostname === 'localhost' 
        ? 'http://localhost:3000/api'
        : '/api',
    timeout: 30000, // 30 seconds
    retries: 3
};

// Application configuration
const appConfig = {
    name: 'Online Courses Platform',
    version: '1.0.0',
    environment: window.location.hostname === 'localhost' ? 'development' : 'production',
    
    // Feature flags
    features: {
        darkMode: true,
        notifications: true,
        analytics: true,
        offlineMode: false
    },
    
    // UI configuration
    ui: {
        itemsPerPage: 12,
        maxFileSize: {
            video: 500 * 1024 * 1024, // 500MB
            pdf: 50 * 1024 * 1024,    // 50MB
            image: 10 * 1024 * 1024   // 10MB
        },
        supportedVideoFormats: ['mp4', 'webm', 'ogg'],
        supportedImageFormats: ['jpg', 'jpeg', 'png', 'gif', 'webp'],
        
        // Toast notification duration
        toastDuration: 5000,
        
        // Animation durations
        animationDuration: {
            fast: 150,
            normal: 300,
            slow: 500
        }
    },
    
    // Validation rules
    validation: {
        email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
        password: {
            minLength: 6,
            requireUppercase: true,
            requireLowercase: true,
            requireNumbers: true,
            requireSpecialChars: false
        },
        name: {
            minLength: 2,
            maxLength: 50
        },
        course: {
            titleMinLength: 3,
            titleMaxLength: 255,
            descriptionMaxLength: 2000
        },
        quiz: {
            titleMinLength: 3,
            titleMaxLength: 255,
            minQuestions: 1,
            maxQuestions: 50
        }
    },
    
    // Error messages
    errorMessages: {
        network: 'Network error. Please check your connection and try again.',
        unauthorized: 'You are not authorized to perform this action.',
        forbidden: 'Access denied. You do not have permission to access this resource.',
        notFound: 'The requested resource was not found.',
        serverError: 'An internal server error occurred. Please try again later.',
        validationError: 'Please check your input and try again.',
        fileUploadError: 'Failed to upload file. Please try again.',
        loginRequired: 'Please log in to continue.',
        sessionExpired: 'Your session has expired. Please log in again.'
    },
    
    // Success messages
    successMessages: {
        login: 'Successfully logged in!',
        logout: 'Successfully logged out!',
        register: 'Account created successfully!',
        profileUpdate: 'Profile updated successfully!',
        courseCreated: 'Course created successfully!',
        courseUpdated: 'Course updated successfully!',
        courseDeleted: 'Course deleted successfully!',
        videoUploaded: 'Video uploaded successfully!',
        quizCreated: 'Quiz created successfully!',
        quizSubmitted: 'Quiz submitted successfully!',
        fileUploaded: 'File uploaded successfully!',
        passwordChanged: 'Password changed successfully!'
    },
    
    // Local storage keys
    storageKeys: {
        authToken: 'courses_auth_token',
        user: 'courses_user',
        preferences: 'courses_preferences',
        theme: 'courses_theme',
        language: 'courses_language'
    },
    
    // Routes configuration
    routes: {
        auth: {
            login: '/auth/login',
            register: '/auth/register',
            logout: '/auth/logout',
            profile: '/auth/profile',
            forgotPassword: '/auth/forgot-password',
            resetPassword: '/auth/reset-password'
        },
        courses: {
            list: '/courses',
            create: '/courses',
            detail: '/courses/:id',
            update: '/courses/:id',
            delete: '/courses/:id',
            enroll: '/courses/:id/enroll',
            progress: '/courses/:id/progress'
        },
        videos: {
            list: '/videos/section/:sectionId',
            create: '/videos',
            detail: '/videos/:id',
            update: '/videos/:id',
            delete: '/videos/:id',
            progress: '/videos/:id/progress'
        },
        quizzes: {
            list: '/quizzes/course/:courseId',
            create: '/quizzes',
            detail: '/quizzes/:id',
            update: '/quizzes/:id',
            delete: '/quizzes/:id',
            submit: '/quizzes/:id/submit',
            attempts: '/quizzes/:id/attempts'
        },
        upload: {
            video: '/upload/video',
            pdf: '/upload/pdf',
            image: '/upload/image',
            delete: '/upload/file'
        },
        users: {
            list: '/users',
            search: '/users/search',
            detail: '/users/:id',
            update: '/users/:id',
            delete: '/users/:id'
        }
    }
};

// Initialize Firebase
if (typeof firebase !== 'undefined') {
    firebase.initializeApp(firebaseConfig);
    window.auth = firebase.auth();
    window.firestore = firebase.firestore();
}

// Initialize Supabase
if (typeof supabase !== 'undefined') {
    window.supabaseClient = supabase.createClient(supabaseConfig.url, supabaseConfig.anonKey);
}

// Export configurations
window.appConfig = appConfig;
window.apiConfig = apiConfig;
window.firebaseConfig = firebaseConfig;
window.supabaseConfig = supabaseConfig;

// Utility function to get configuration values
window.getConfig = function(path, defaultValue = null) {
    const keys = path.split('.');
    let value = appConfig;
    
    for (const key of keys) {
        if (value && typeof value === 'object' && key in value) {
            value = value[key];
        } else {
            return defaultValue;
        }
    }
    
    return value;
};

// Environment-specific configurations
if (appConfig.environment === 'development') {
    // Enable debug mode
    window.DEBUG = true;
    console.log('🚀 Application started in development mode');
    console.log('📋 Configuration:', appConfig);
} else {
    // Production optimizations
    window.DEBUG = false;
    
    // Disable console logs in production
    if (!window.DEBUG) {
        console.log = () => {};
        console.warn = () => {};
        console.error = () => {};
    }
}

// Global error handler
window.addEventListener('error', (event) => {
    if (window.DEBUG) {
        console.error('Global error:', event.error);
    }
    
    // You can send errors to a logging service here
    // Example: sendErrorToLoggingService(event.error);
});

// Unhandled promise rejection handler
window.addEventListener('unhandledrejection', (event) => {
    if (window.DEBUG) {
        console.error('Unhandled promise rejection:', event.reason);
    }
    
    // Prevent the default browser behavior
    event.preventDefault();
});

console.log('✅ Configuration loaded successfully');
