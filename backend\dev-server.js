const express = require('express');
const cors = require('cors');
const path = require('path');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3000;

// CORS configuration
app.use(cors({
  origin: ['http://localhost:3001', 'http://127.0.0.1:3001', 'http://localhost:8080'],
  credentials: true
}));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Mock data for development
const mockData = {
  users: [
    {
      id: '1',
      email: '<EMAIL>',
      firstName: 'مدير',
      lastName: 'النظام',
      role: 'admin',
      isActive: true,
      avatarUrl: 'https://via.placeholder.com/40',
      createdAt: new Date().toISOString()
    },
    {
      id: '2',
      email: '<EMAIL>',
      firstName: 'أحمد',
      lastName: 'محمد',
      role: 'instructor',
      isActive: true,
      avatarUrl: 'https://via.placeholder.com/40',
      bio: 'مدرب معتمد في البرمجة',
      createdAt: new Date().toISOString()
    },
    {
      id: '3',
      email: '<EMAIL>',
      firstName: 'فاطمة',
      lastName: 'علي',
      role: 'student',
      isActive: true,
      avatarUrl: 'https://via.placeholder.com/40',
      createdAt: new Date().toISOString()
    }
  ],
  courses: [
    {
      id: '1',
      instructorId: '2',
      title: 'مقدمة في البرمجة',
      description: 'تعلم أساسيات البرمجة من الصفر باستخدام JavaScript. هذه الدورة مصممة للمبتدئين الذين يريدون دخول عالم البرمجة.',
      category: 'programming',
      difficultyLevel: 'beginner',
      estimatedDuration: 480,
      price: 0,
      thumbnailUrl: 'https://via.placeholder.com/300x200?text=دورة+البرمجة',
      isPublished: true,
      instructor: {
        id: '2',
        firstName: 'أحمد',
        lastName: 'محمد',
        avatarUrl: 'https://via.placeholder.com/40',
        bio: 'مدرب معتمد في البرمجة'
      },
      sections: [
        {
          id: '1',
          title: 'الأساسيات',
          description: 'تعلم الأساسيات الضرورية للبرمجة',
          orderIndex: 1,
          videos: [
            {
              id: '1',
              title: 'مقدمة عن البرمجة',
              description: 'تعرف على أساسيات البرمجة وأهميتها في العصر الحديث',
              duration: 600,
              orderIndex: 1,
              isPreview: true,
              videoUrl: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
              thumbnailUrl: 'https://via.placeholder.com/300x200?text=فيديو+1',
              isCompleted: false,
              pdfs: [
                {
                  id: '1',
                  title: 'ملخص الدرس الأول',
                  description: 'ملخص شامل لأساسيات البرمجة',
                  pdfUrl: 'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf',
                  fileSize: 1024000
                }
              ]
            },
            {
              id: '2',
              title: 'المتغيرات والثوابت',
              description: 'تعلم كيفية استخدام المتغيرات والثوابت في البرمجة',
              duration: 720,
              orderIndex: 2,
              isPreview: false,
              videoUrl: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_2mb.mp4',
              thumbnailUrl: 'https://via.placeholder.com/300x200?text=فيديو+2',
              isCompleted: false
            }
          ]
        },
        {
          id: '2',
          title: 'الدوال والكائنات',
          description: 'تعلم البرمجة الكائنية والدوال',
          orderIndex: 2,
          videos: [
            {
              id: '3',
              title: 'مقدمة عن الدوال',
              description: 'تعلم كيفية إنشاء واستخدام الدوال',
              duration: 900,
              orderIndex: 1,
              isPreview: false,
              videoUrl: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_5mb.mp4',
              thumbnailUrl: 'https://via.placeholder.com/300x200?text=فيديو+3',
              isCompleted: false
            }
          ]
        }
      ],
      progress: 25,
      isEnrolled: true,
      createdAt: new Date().toISOString()
    },
    {
      id: '2',
      instructorId: '2',
      title: 'تطوير المواقع الإلكترونية',
      description: 'تعلم تطوير المواقع الإلكترونية باستخدام HTML, CSS, و JavaScript',
      category: 'web-development',
      difficultyLevel: 'intermediate',
      estimatedDuration: 720,
      price: 299,
      thumbnailUrl: 'https://via.placeholder.com/300x200?text=تطوير+المواقع',
      isPublished: true,
      instructor: {
        id: '2',
        firstName: 'أحمد',
        lastName: 'محمد'
      },
      sections: [],
      progress: 0,
      isEnrolled: false,
      createdAt: new Date().toISOString()
    }
  ],
  quizzes: [
    {
      id: '1',
      courseId: '1',
      title: 'اختبار الأساسيات',
      description: 'اختبر معرفتك بأساسيات البرمجة',
      questions: [
        {
          type: 'multiple_choice',
          question: 'ما هي لغة البرمجة؟',
          options: ['أداة للتواصل مع الحاسوب', 'نوع من الطعام', 'لعبة فيديو', 'برنامج تلفزيوني'],
          correctAnswer: 0
        },
        {
          type: 'true_false',
          question: 'JavaScript هي نفسها Java؟',
          correctAnswer: false
        },
        {
          type: 'short_answer',
          question: 'اذكر ثلاث لغات برمجة مشهورة',
          correctAnswer: 'JavaScript, Python, Java'
        }
      ],
      passingScore: 70,
      timeLimit: 30,
      maxAttempts: 3,
      isActive: true,
      studentAttempts: []
    }
  ]
};

// Mock API Routes
app.get('/api/health', (req, res) => {
  res.json({
    success: true,
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development',
    version: '1.0.0',
    message: 'منصة الدورات التدريبية - خادم تطوير محلي'
  });
});

// Auth routes
app.post('/api/auth/login', (req, res) => {
  const { idToken } = req.body;
  
  // Mock authentication - في الواقع ستتحقق من Firebase
  if (idToken) {
    const user = mockData.users[0]; // Return admin user for demo
    res.json({
      success: true,
      data: {
        user: user,
        jwtToken: 'mock-jwt-token-' + user.id
      }
    });
  } else {
    res.status(401).json({
      success: false,
      message: 'بيانات الدخول غير صحيحة'
    });
  }
});

app.post('/api/auth/register', (req, res) => {
  const { email, firstName, lastName, role } = req.body;
  const newUser = {
    id: String(mockData.users.length + 1),
    email,
    firstName,
    lastName,
    role: role || 'student',
    isActive: true,
    avatarUrl: 'https://via.placeholder.com/40',
    createdAt: new Date().toISOString()
  };
  
  mockData.users.push(newUser);
  
  res.json({
    success: true,
    data: {
      user: newUser,
      jwtToken: 'mock-jwt-token-' + newUser.id
    }
  });
});

app.get('/api/auth/profile', (req, res) => {
  res.json({
    success: true,
    data: {
      user: mockData.users[0]
    }
  });
});

// Courses routes
app.get('/api/courses', (req, res) => {
  res.json({
    success: true,
    data: {
      courses: mockData.courses
    }
  });
});

app.get('/api/courses/:id', (req, res) => {
  const course = mockData.courses.find(c => c.id === req.params.id);
  if (course) {
    res.json({
      success: true,
      data: {
        course: course
      }
    });
  } else {
    res.status(404).json({
      success: false,
      message: 'الدورة غير موجودة'
    });
  }
});

// Videos routes
app.get('/api/videos/section/:sectionId', (req, res) => {
  const sectionId = req.params.sectionId;
  let videos = [];
  
  for (const course of mockData.courses) {
    const section = course.sections?.find(s => s.id === sectionId);
    if (section) {
      videos = section.videos || [];
      break;
    }
  }
  
  res.json({
    success: true,
    data: {
      videos: videos
    }
  });
});

app.get('/api/videos/:id', (req, res) => {
  let video = null;
  for (const course of mockData.courses) {
    for (const section of course.sections || []) {
      video = section.videos?.find(v => v.id === req.params.id);
      if (video) break;
    }
    if (video) break;
  }
  
  if (video) {
    res.json({
      success: true,
      data: {
        video: video
      }
    });
  } else {
    res.status(404).json({
      success: false,
      message: 'الفيديو غير موجود'
    });
  }
});

app.post('/api/videos/:id/progress', (req, res) => {
  res.json({
    success: true,
    message: 'تم حفظ التقدم بنجاح'
  });
});

// Quizzes routes
app.get('/api/quizzes/course/:courseId', (req, res) => {
  const quizzes = mockData.quizzes.filter(q => q.courseId === req.params.courseId);
  res.json({
    success: true,
    data: {
      quizzes: quizzes
    }
  });
});

app.get('/api/quizzes/:id', (req, res) => {
  const quiz = mockData.quizzes.find(q => q.id === req.params.id);
  if (quiz) {
    res.json({
      success: true,
      data: {
        quiz: quiz
      }
    });
  } else {
    res.status(404).json({
      success: false,
      message: 'الاختبار غير موجود'
    });
  }
});

app.post('/api/quizzes/:id/submit', (req, res) => {
  const { answers, timeTaken } = req.body;
  const quiz = mockData.quizzes.find(q => q.id === req.params.id);
  
  if (quiz) {
    // حساب النتيجة (مبسط)
    let correctAnswers = 0;
    answers.forEach((answer, index) => {
      if (quiz.questions[index] && answer === quiz.questions[index].correctAnswer) {
        correctAnswers++;
      }
    });
    
    const score = Math.round((correctAnswers / quiz.questions.length) * 100);
    const passed = score >= quiz.passingScore;
    
    const attempt = {
      id: Date.now().toString(),
      score: score,
      passed: passed,
      timeTaken: timeTaken,
      completedAt: new Date().toISOString()
    };
    
    res.json({
      success: true,
      message: 'تم تسليم الاختبار بنجاح',
      data: {
        attempt: attempt
      }
    });
  } else {
    res.status(404).json({
      success: false,
      message: 'الاختبار غير موجود'
    });
  }
});

// Upload routes (mock)
app.post('/api/upload/video', (req, res) => {
  res.json({
    success: true,
    message: 'تم رفع الفيديو بنجاح (وهمي)',
    data: {
      fileName: 'video.mp4',
      fileUrl: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4'
    }
  });
});

// Users routes
app.get('/api/users', (req, res) => {
  res.json({
    success: true,
    data: {
      users: mockData.users
    }
  });
});

// Serve static files from frontend
app.use(express.static(path.join(__dirname, '../frontend')));

// Catch-all handler for SPA
app.get('*', (req, res) => {
  if (req.path.startsWith('/api/')) {
    res.status(404).json({ 
      success: false,
      error: 'API endpoint not found' 
    });
  } else {
    res.sendFile(path.join(__dirname, '../frontend/index.html'));
  }
});

// Global error handler
app.use((err, req, res, next) => {
  console.error('Error:', err);
  res.status(err.status || 500).json({
    success: false,
    error: process.env.NODE_ENV === 'development' ? err.message : 'Internal server error'
  });
});

// Start server
app.listen(PORT, () => {
  console.log('🚀 ========================================');
  console.log('🎓 منصة الدورات التدريبية الإلكترونية');
  console.log('🚀 ========================================');
  console.log(`📡 Server: http://localhost:${PORT}`);
  console.log(`🌐 Frontend: http://localhost:${PORT}`);
  console.log(`📊 API Health: http://localhost:${PORT}/api/health`);
  console.log('🚀 ========================================');
  console.log('✅ الخادم جاهز للاستخدام!');
  console.log('📝 بيانات تجريبية متاحة للاختبار');
  console.log('🚀 ========================================');
});

module.exports = app;
