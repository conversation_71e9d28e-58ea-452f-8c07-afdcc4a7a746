// مشغل الفيديو المخصص
class VideoPlayer {
    constructor(containerId, options = {}) {
        this.container = document.getElementById(containerId);
        this.options = {
            autoplay: false,
            controls: true,
            playbackRates: [0.5, 0.75, 1, 1.25, 1.5, 2],
            saveProgress: true,
            ...options
        };
        
        this.video = null;
        this.currentVideo = null;
        this.progress = 0;
        this.isPlaying = false;
        this.currentTime = 0;
        this.duration = 0;
        
        this.init();
    }

    init() {
        if (!this.container) {
            console.error('حاوية مشغل الفيديو غير موجودة');
            return;
        }

        this.createPlayer();
        this.setupEventListeners();
    }

    createPlayer() {
        this.container.innerHTML = `
            <div class="video-player-wrapper bg-black rounded-lg overflow-hidden">
                <div class="relative">
                    <video class="video-element w-full h-auto" preload="metadata">
                        متصفحك لا يدعم تشغيل الفيديو.
                    </video>
                    
                    <!-- طبقة التحكم -->
                    <div class="video-overlay absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center opacity-0 transition-opacity duration-300">
                        <button class="play-pause-btn bg-white bg-opacity-20 rounded-full p-4 hover:bg-opacity-30 transition-all duration-200">
                            <i data-feather="play" class="h-12 w-12 text-white"></i>
                        </button>
                    </div>
                    
                    <!-- شريط التحكم -->
                    <div class="video-controls absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-4 opacity-0 transition-opacity duration-300">
                        <div class="flex items-center space-x-4 rtl:space-x-reverse">
                            <!-- زر التشغيل/الإيقاف -->
                            <button class="control-play-pause text-white hover:text-primary-400 transition-colors">
                                <i data-feather="play" class="h-5 w-5"></i>
                            </button>
                            
                            <!-- شريط التقدم -->
                            <div class="flex-1">
                                <div class="progress-container relative">
                                    <div class="progress-track bg-gray-600 h-1 rounded-full cursor-pointer">
                                        <div class="progress-bar bg-primary-500 h-1 rounded-full transition-all duration-200" style="width: 0%"></div>
                                        <div class="progress-handle absolute top-1/2 transform -translate-y-1/2 w-3 h-3 bg-primary-500 rounded-full opacity-0 transition-opacity duration-200" style="left: 0%"></div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- الوقت -->
                            <div class="time-display text-white text-sm">
                                <span class="current-time">0:00</span>
                                <span class="mx-1">/</span>
                                <span class="duration">0:00</span>
                            </div>
                            
                            <!-- سرعة التشغيل -->
                            <div class="playback-rate relative">
                                <button class="rate-btn text-white hover:text-primary-400 transition-colors text-sm">1x</button>
                                <div class="rate-menu absolute bottom-full right-0 mb-2 bg-black bg-opacity-80 rounded-md py-2 hidden">
                                    ${this.options.playbackRates.map(rate => 
                                        `<button class="rate-option block w-full px-4 py-1 text-white hover:bg-primary-500 text-sm" data-rate="${rate}">${rate}x</button>`
                                    ).join('')}
                                </div>
                            </div>
                            
                            <!-- مستوى الصوت -->
                            <div class="volume-control flex items-center space-x-2 rtl:space-x-reverse">
                                <button class="volume-btn text-white hover:text-primary-400 transition-colors">
                                    <i data-feather="volume-2" class="h-5 w-5"></i>
                                </button>
                                <div class="volume-slider hidden md:block">
                                    <input type="range" min="0" max="100" value="100" class="w-16 h-1 bg-gray-600 rounded-lg appearance-none cursor-pointer">
                                </div>
                            </div>
                            
                            <!-- ملء الشاشة -->
                            <button class="fullscreen-btn text-white hover:text-primary-400 transition-colors">
                                <i data-feather="maximize" class="h-5 w-5"></i>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- معلومات الفيديو -->
                <div class="video-info bg-gray-900 text-white p-4">
                    <h3 class="video-title text-lg font-medium mb-2">عنوان الفيديو</h3>
                    <p class="video-description text-gray-300 text-sm">وصف الفيديو</p>
                </div>
            </div>
        `;

        this.video = this.container.querySelector('.video-element');
        feather.replace();
    }

    setupEventListeners() {
        const overlay = this.container.querySelector('.video-overlay');
        const controls = this.container.querySelector('.video-controls');
        const playPauseBtn = this.container.querySelector('.play-pause-btn');
        const controlPlayPause = this.container.querySelector('.control-play-pause');
        const progressTrack = this.container.querySelector('.progress-track');
        const progressBar = this.container.querySelector('.progress-bar');
        const progressHandle = this.container.querySelector('.progress-handle');
        const currentTimeEl = this.container.querySelector('.current-time');
        const durationEl = this.container.querySelector('.duration');
        const rateBtn = this.container.querySelector('.rate-btn');
        const rateMenu = this.container.querySelector('.rate-menu');
        const volumeBtn = this.container.querySelector('.volume-btn');
        const volumeSlider = this.container.querySelector('.volume-slider input');
        const fullscreenBtn = this.container.querySelector('.fullscreen-btn');

        // إظهار/إخفاء التحكم
        this.container.addEventListener('mouseenter', () => {
            overlay.classList.remove('opacity-0');
            controls.classList.remove('opacity-0');
        });

        this.container.addEventListener('mouseleave', () => {
            if (!this.isPlaying) {
                overlay.classList.add('opacity-0');
            }
            controls.classList.add('opacity-0');
        });

        // أحداث الفيديو
        this.video.addEventListener('loadedmetadata', () => {
            this.duration = this.video.duration;
            durationEl.textContent = this.formatTime(this.duration);
        });

        this.video.addEventListener('timeupdate', () => {
            this.currentTime = this.video.currentTime;
            this.progress = (this.currentTime / this.duration) * 100;
            
            progressBar.style.width = `${this.progress}%`;
            progressHandle.style.left = `${this.progress}%`;
            currentTimeEl.textContent = this.formatTime(this.currentTime);
            
            // حفظ التقدم
            if (this.options.saveProgress && this.currentVideo) {
                this.saveProgress();
            }
        });

        this.video.addEventListener('play', () => {
            this.isPlaying = true;
            this.updatePlayButton(true);
            overlay.classList.add('opacity-0');
        });

        this.video.addEventListener('pause', () => {
            this.isPlaying = false;
            this.updatePlayButton(false);
            overlay.classList.remove('opacity-0');
        });

        this.video.addEventListener('ended', () => {
            this.isPlaying = false;
            this.updatePlayButton(false);
            overlay.classList.remove('opacity-0');
            
            // تسجيل إكمال الفيديو
            if (this.currentVideo) {
                this.markVideoCompleted();
            }
        });

        // أزرار التشغيل
        playPauseBtn.addEventListener('click', () => this.togglePlayPause());
        controlPlayPause.addEventListener('click', () => this.togglePlayPause());

        // شريط التقدم
        progressTrack.addEventListener('click', (e) => {
            const rect = progressTrack.getBoundingClientRect();
            const clickX = e.clientX - rect.left;
            const percentage = clickX / rect.width;
            this.seekTo(percentage * this.duration);
        });

        // سرعة التشغيل
        rateBtn.addEventListener('click', () => {
            rateMenu.classList.toggle('hidden');
        });

        this.container.querySelectorAll('.rate-option').forEach(option => {
            option.addEventListener('click', () => {
                const rate = parseFloat(option.dataset.rate);
                this.setPlaybackRate(rate);
                rateBtn.textContent = `${rate}x`;
                rateMenu.classList.add('hidden');
            });
        });

        // مستوى الصوت
        volumeBtn.addEventListener('click', () => this.toggleMute());
        
        if (volumeSlider) {
            volumeSlider.addEventListener('input', (e) => {
                this.setVolume(e.target.value / 100);
            });
        }

        // ملء الشاشة
        fullscreenBtn.addEventListener('click', () => this.toggleFullscreen());

        // اختصارات لوحة المفاتيح
        document.addEventListener('keydown', (e) => {
            if (this.container.contains(document.activeElement) || e.target === this.video) {
                this.handleKeyboard(e);
            }
        });
    }

    // تحميل فيديو جديد
    async loadVideo(videoData) {
        try {
            this.currentVideo = videoData;
            this.video.src = videoData.videoUrl;
            
            // تحديث معلومات الفيديو
            const titleEl = this.container.querySelector('.video-title');
            const descEl = this.container.querySelector('.video-description');
            
            if (titleEl) titleEl.textContent = videoData.title;
            if (descEl) descEl.textContent = videoData.description || '';

            // تحميل التقدم المحفوظ
            if (this.options.saveProgress) {
                await this.loadProgress();
            }

        } catch (error) {
            console.error('خطأ في تحميل الفيديو:', error);
            showToast('فشل في تحميل الفيديو', 'error');
        }
    }

    // تشغيل/إيقاف
    togglePlayPause() {
        if (this.video.paused) {
            this.video.play();
        } else {
            this.video.pause();
        }
    }

    // تحديث أزرار التشغيل
    updatePlayButton(isPlaying) {
        const playIcon = isPlaying ? 'pause' : 'play';
        
        this.container.querySelectorAll('[data-feather="play"], [data-feather="pause"]').forEach(icon => {
            icon.setAttribute('data-feather', playIcon);
        });
        
        feather.replace();
    }

    // الانتقال إلى وقت محدد
    seekTo(time) {
        this.video.currentTime = time;
    }

    // تعيين سرعة التشغيل
    setPlaybackRate(rate) {
        this.video.playbackRate = rate;
    }

    // تعيين مستوى الصوت
    setVolume(volume) {
        this.video.volume = volume;
        this.updateVolumeIcon(volume);
    }

    // كتم/إلغاء كتم الصوت
    toggleMute() {
        this.video.muted = !this.video.muted;
        this.updateVolumeIcon(this.video.muted ? 0 : this.video.volume);
    }

    // تحديث أيقونة الصوت
    updateVolumeIcon(volume) {
        const volumeBtn = this.container.querySelector('.volume-btn i');
        let icon = 'volume-2';
        
        if (volume === 0) {
            icon = 'volume-x';
        } else if (volume < 0.5) {
            icon = 'volume-1';
        }
        
        volumeBtn.setAttribute('data-feather', icon);
        feather.replace();
    }

    // ملء الشاشة
    toggleFullscreen() {
        if (!document.fullscreenElement) {
            this.container.requestFullscreen();
        } else {
            document.exitFullscreen();
        }
    }

    // اختصارات لوحة المفاتيح
    handleKeyboard(e) {
        switch (e.code) {
            case 'Space':
                e.preventDefault();
                this.togglePlayPause();
                break;
            case 'ArrowLeft':
                e.preventDefault();
                this.seekTo(this.currentTime - 10);
                break;
            case 'ArrowRight':
                e.preventDefault();
                this.seekTo(this.currentTime + 10);
                break;
            case 'ArrowUp':
                e.preventDefault();
                this.setVolume(Math.min(1, this.video.volume + 0.1));
                break;
            case 'ArrowDown':
                e.preventDefault();
                this.setVolume(Math.max(0, this.video.volume - 0.1));
                break;
            case 'KeyF':
                e.preventDefault();
                this.toggleFullscreen();
                break;
            case 'KeyM':
                e.preventDefault();
                this.toggleMute();
                break;
        }
    }

    // تنسيق الوقت
    formatTime(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = Math.floor(seconds % 60);

        if (hours > 0) {
            return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        } else {
            return `${minutes}:${secs.toString().padStart(2, '0')}`;
        }
    }

    // حفظ التقدم
    async saveProgress() {
        if (!this.currentVideo || !window.authManager.isAuthenticated) return;

        try {
            const progressData = {
                watchedDuration: Math.floor(this.currentTime),
                isCompleted: this.progress >= 90 // اعتبار الفيديو مكتملاً عند 90%
            };

            await window.api.updateVideoProgress(this.currentVideo.id, progressData);
        } catch (error) {
            console.error('خطأ في حفظ التقدم:', error);
        }
    }

    // تحميل التقدم المحفوظ
    async loadProgress() {
        if (!this.currentVideo || !window.authManager.isAuthenticated) return;

        try {
            const response = await window.api.getVideo(this.currentVideo.id, { includeProgress: true });
            const progress = response.data.video.progress;

            if (progress && progress.watchedDuration > 0) {
                this.video.currentTime = progress.watchedDuration;
            }
        } catch (error) {
            console.error('خطأ في تحميل التقدم:', error);
        }
    }

    // تسجيل إكمال الفيديو
    async markVideoCompleted() {
        if (!this.currentVideo || !window.authManager.isAuthenticated) return;

        try {
            await window.api.updateVideoProgress(this.currentVideo.id, {
                watchedDuration: Math.floor(this.duration),
                isCompleted: true
            });

            showToast('تم إكمال الفيديو!', 'success');
        } catch (error) {
            console.error('خطأ في تسجيل إكمال الفيديو:', error);
        }
    }

    // تدمير المشغل
    destroy() {
        if (this.video) {
            this.video.pause();
            this.video.src = '';
        }
        
        if (this.container) {
            this.container.innerHTML = '';
        }
    }
}

// تصدير الفئة
window.VideoPlayer = VideoPlayer;
