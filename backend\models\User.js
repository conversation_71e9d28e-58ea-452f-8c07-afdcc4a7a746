const { supabase } = require('../config/database');

class User {
  constructor(data) {
    this.id = data.id;
    this.firebaseUid = data.firebase_uid;
    this.email = data.email;
    this.role = data.role;
    this.firstName = data.first_name;
    this.lastName = data.last_name;
    this.avatarUrl = data.avatar_url;
    this.bio = data.bio;
    this.phone = data.phone;
    this.isActive = data.is_active;
    this.createdAt = data.created_at;
    this.updatedAt = data.updated_at;
  }

  static async findByFirebaseUid(firebaseUid) {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('firebase_uid', firebaseUid)
      .single();

    if (error) {
      if (error.code === 'PGRST116') return null; // Not found
      throw error;
    }

    return new User(data);
  }

  static async findByEmail(email) {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('email', email)
      .single();

    if (error) {
      if (error.code === 'PGRST116') return null; // Not found
      throw error;
    }

    return new User(data);
  }

  static async findById(id) {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') return null; // Not found
      throw error;
    }

    return new User(data);
  }

  static async create(userData) {
    const { data, error } = await supabase
      .from('users')
      .insert({
        firebase_uid: userData.firebaseUid,
        email: userData.email,
        role: userData.role || 'student',
        first_name: userData.firstName,
        last_name: userData.lastName,
        avatar_url: userData.avatarUrl,
        bio: userData.bio,
        phone: userData.phone
      })
      .select()
      .single();

    if (error) throw error;

    return new User(data);
  }

  async update(updateData) {
    const { data, error } = await supabase
      .from('users')
      .update({
        first_name: updateData.firstName || this.firstName,
        last_name: updateData.lastName || this.lastName,
        avatar_url: updateData.avatarUrl || this.avatarUrl,
        bio: updateData.bio || this.bio,
        phone: updateData.phone || this.phone,
        is_active: updateData.isActive !== undefined ? updateData.isActive : this.isActive
      })
      .eq('id', this.id)
      .select()
      .single();

    if (error) throw error;

    // Update instance properties
    Object.assign(this, new User(data));
    return this;
  }

  static async findByRole(role, limit = 50, offset = 0) {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('role', role)
      .eq('is_active', true)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) throw error;

    return data.map(user => new User(user));
  }

  static async search(query, role = null, limit = 20) {
    let queryBuilder = supabase
      .from('users')
      .select('*')
      .eq('is_active', true);

    if (role) {
      queryBuilder = queryBuilder.eq('role', role);
    }

    queryBuilder = queryBuilder
      .or(`first_name.ilike.%${query}%,last_name.ilike.%${query}%,email.ilike.%${query}%`)
      .order('created_at', { ascending: false })
      .limit(limit);

    const { data, error } = await queryBuilder;

    if (error) throw error;

    return data.map(user => new User(user));
  }

  async delete() {
    const { error } = await supabase
      .from('users')
      .update({ is_active: false })
      .eq('id', this.id);

    if (error) throw error;

    this.isActive = false;
    return this;
  }

  toJSON() {
    return {
      id: this.id,
      firebaseUid: this.firebaseUid,
      email: this.email,
      role: this.role,
      firstName: this.firstName,
      lastName: this.lastName,
      fullName: `${this.firstName} ${this.lastName}`,
      avatarUrl: this.avatarUrl,
      bio: this.bio,
      phone: this.phone,
      isActive: this.isActive,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    };
  }
}

module.exports = User;
