rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can read and write their own user document
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Course progress and real-time data
    match /courseProgress/{progressId} {
      allow read, write: if request.auth != null && 
        (request.auth.uid == resource.data.studentId || 
         request.auth.uid in resource.data.instructorIds);
    }
    
    // Real-time notifications
    match /notifications/{notificationId} {
      allow read: if request.auth != null && request.auth.uid == resource.data.userId;
      allow write: if request.auth != null && 
        (request.auth.uid == resource.data.userId || 
         request.auth.uid in resource.data.adminIds);
    }
    
    // Live chat/discussions (if implemented)
    match /discussions/{discussionId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && 
        request.auth.uid == request.resource.data.authorId;
    }
    
    // Admin-only collections
    match /adminData/{document=**} {
      allow read, write: if request.auth != null && 
        request.auth.token.role == 'admin';
    }
    
    // Default deny all other access
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
