const multer = require('multer');
const { v4: uuidv4 } = require('uuid');
const path = require('path');
const { storageHelpers } = require('../config/database');

class UploadController {
  // Configure multer for different file types
  static getMulterConfig(fileType) {
    const storage = multer.memoryStorage();
    
    const fileFilter = (req, file, cb) => {
      switch (fileType) {
        case 'video':
          if (file.mimetype.startsWith('video/')) {
            cb(null, true);
          } else {
            cb(new Error('Only video files are allowed'), false);
          }
          break;
        case 'pdf':
          if (file.mimetype === 'application/pdf') {
            cb(null, true);
          } else {
            cb(new Error('Only PDF files are allowed'), false);
          }
          break;
        case 'image':
          if (file.mimetype.startsWith('image/')) {
            cb(null, true);
          } else {
            cb(new Error('Only image files are allowed'), false);
          }
          break;
        default:
          cb(new Error('Invalid file type'), false);
      }
    };

    const limits = {
      fileSize: fileType === 'video' 
        ? parseInt(process.env.MAX_VIDEO_SIZE) || 524288000 // 500MB
        : parseInt(process.env.MAX_PDF_SIZE) || 52428800    // 50MB
    };

    return multer({
      storage,
      fileFilter,
      limits
    });
  }

  // Upload video file
  static async uploadVideo(req, res) {
    try {
      if (!req.file) {
        return res.status(400).json({
          success: false,
          message: 'No video file provided'
        });
      }

      const file = req.file;
      const fileExtension = path.extname(file.originalname);
      const fileName = `${uuidv4()}${fileExtension}`;
      const filePath = `videos/${req.user.id}/${fileName}`;

      // Upload to Supabase Storage
      const uploadResult = await storageHelpers.uploadFile(
        'videos',
        filePath,
        file.buffer,
        {
          contentType: file.mimetype,
          cacheControl: '3600'
        }
      );

      // Get public URL
      const publicUrl = await storageHelpers.getFileUrl('videos', filePath);

      res.json({
        success: true,
        message: 'Video uploaded successfully',
        data: {
          fileName: fileName,
          originalName: file.originalname,
          filePath: filePath,
          fileUrl: publicUrl,
          fileSize: file.size,
          mimeType: file.mimetype
        }
      });

    } catch (error) {
      console.error('Video upload error:', error);
      
      if (error.message.includes('File size too large')) {
        return res.status(400).json({
          success: false,
          message: 'Video file is too large. Maximum size is 500MB.'
        });
      }

      res.status(500).json({
        success: false,
        message: 'Failed to upload video'
      });
    }
  }

  // Upload PDF file
  static async uploadPdf(req, res) {
    try {
      if (!req.file) {
        return res.status(400).json({
          success: false,
          message: 'No PDF file provided'
        });
      }

      const file = req.file;
      const fileName = `${uuidv4()}.pdf`;
      const filePath = `pdfs/${req.user.id}/${fileName}`;

      // Upload to Supabase Storage
      const uploadResult = await storageHelpers.uploadFile(
        'pdfs',
        filePath,
        file.buffer,
        {
          contentType: 'application/pdf',
          cacheControl: '3600'
        }
      );

      // Get public URL
      const publicUrl = await storageHelpers.getFileUrl('pdfs', filePath);

      res.json({
        success: true,
        message: 'PDF uploaded successfully',
        data: {
          fileName: fileName,
          originalName: file.originalname,
          filePath: filePath,
          fileUrl: publicUrl,
          fileSize: file.size,
          mimeType: file.mimetype
        }
      });

    } catch (error) {
      console.error('PDF upload error:', error);
      
      if (error.message.includes('File size too large')) {
        return res.status(400).json({
          success: false,
          message: 'PDF file is too large. Maximum size is 50MB.'
        });
      }

      res.status(500).json({
        success: false,
        message: 'Failed to upload PDF'
      });
    }
  }

  // Upload image (for thumbnails, avatars, etc.)
  static async uploadImage(req, res) {
    try {
      if (!req.file) {
        return res.status(400).json({
          success: false,
          message: 'No image file provided'
        });
      }

      const file = req.file;
      const fileExtension = path.extname(file.originalname);
      const fileName = `${uuidv4()}${fileExtension}`;
      const { type = 'general' } = req.body; // avatar, thumbnail, general
      const filePath = `images/${type}/${req.user.id}/${fileName}`;

      // Upload to Supabase Storage
      const uploadResult = await storageHelpers.uploadFile(
        'avatars', // Using avatars bucket for all images
        filePath,
        file.buffer,
        {
          contentType: file.mimetype,
          cacheControl: '3600'
        }
      );

      // Get public URL
      const publicUrl = await storageHelpers.getFileUrl('avatars', filePath);

      res.json({
        success: true,
        message: 'Image uploaded successfully',
        data: {
          fileName: fileName,
          originalName: file.originalname,
          filePath: filePath,
          fileUrl: publicUrl,
          fileSize: file.size,
          mimeType: file.mimetype,
          type: type
        }
      });

    } catch (error) {
      console.error('Image upload error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to upload image'
      });
    }
  }

  // Delete file
  static async deleteFile(req, res) {
    try {
      const { bucket, filePath } = req.body;

      if (!bucket || !filePath) {
        return res.status(400).json({
          success: false,
          message: 'Bucket and file path are required'
        });
      }

      // Verify user owns the file (check if path contains user ID)
      if (!filePath.includes(req.user.id) && req.user.role !== 'admin') {
        return res.status(403).json({
          success: false,
          message: 'Access denied. You can only delete your own files.'
        });
      }

      await storageHelpers.deleteFile(bucket, filePath);

      res.json({
        success: true,
        message: 'File deleted successfully'
      });

    } catch (error) {
      console.error('Delete file error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to delete file'
      });
    }
  }

  // List user's files
  static async listFiles(req, res) {
    try {
      const { bucket, folder = '' } = req.query;

      if (!bucket) {
        return res.status(400).json({
          success: false,
          message: 'Bucket is required'
        });
      }

      // Construct user-specific folder path
      const userFolder = folder ? `${folder}/${req.user.id}` : req.user.id;

      const files = await storageHelpers.listFiles(bucket, userFolder);

      res.json({
        success: true,
        data: {
          files: files.map(file => ({
            name: file.name,
            size: file.metadata?.size,
            lastModified: file.updated_at,
            contentType: file.metadata?.mimetype
          }))
        }
      });

    } catch (error) {
      console.error('List files error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to list files'
      });
    }
  }

  // Get file info
  static async getFileInfo(req, res) {
    try {
      const { bucket, filePath } = req.params;

      // Verify user owns the file or is admin
      if (!filePath.includes(req.user.id) && req.user.role !== 'admin') {
        return res.status(403).json({
          success: false,
          message: 'Access denied'
        });
      }

      const publicUrl = await storageHelpers.getFileUrl(bucket, filePath);

      res.json({
        success: true,
        data: {
          filePath,
          fileUrl: publicUrl,
          bucket
        }
      });

    } catch (error) {
      console.error('Get file info error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get file info'
      });
    }
  }
}

module.exports = UploadController;
