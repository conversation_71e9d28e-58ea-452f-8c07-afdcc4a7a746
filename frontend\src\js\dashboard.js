// Dashboard module
class DashboardManager {
    constructor() {
        this.currentUser = null;
        this.dashboardData = null;
    }

    async loadDashboard() {
        try {
            this.currentUser = window.authManager.currentUser;
            if (!this.currentUser) {
                throw new Error('User not authenticated');
            }

            const container = document.getElementById('dashboard-container');
            if (!container) return;

            // Show loading state
            container.innerHTML = this.getLoadingHTML();

            // Load dashboard based on user role
            switch (this.currentUser.role) {
                case 'admin':
                    await this.loadAdminDashboard();
                    break;
                case 'instructor':
                    await this.loadInstructorDashboard();
                    break;
                case 'student':
                    await this.loadStudentDashboard();
                    break;
                default:
                    throw new Error('Unknown user role');
            }

        } catch (error) {
            console.error('Dashboard loading error:', error);
            this.showError('Failed to load dashboard');
        }
    }

    async loadAdminDashboard() {
        const container = document.getElementById('dashboard-container');
        
        try {
            // Fetch admin statistics
            const [usersData, coursesData] = await Promise.all([
                window.api.getUsers({ limit: 5 }),
                window.api.getCourses({ limit: 5 })
            ]);

            const html = `
                <div class="p-6">
                    <div class="mb-8">
                        <h1 class="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
                        <p class="text-gray-600">Welcome back, ${this.currentUser.firstName}!</p>
                    </div>

                    <!-- Statistics Cards -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                        <div class="stat-card">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <i data-feather="users" class="h-8 w-8 text-blue-500"></i>
                                </div>
                                <div class="ml-4">
                                    <p class="stat-label">Total Users</p>
                                    <p class="stat-value">${usersData.data?.users?.length || 0}</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="stat-card">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <i data-feather="book" class="h-8 w-8 text-green-500"></i>
                                </div>
                                <div class="ml-4">
                                    <p class="stat-label">Total Courses</p>
                                    <p class="stat-value">${coursesData.data?.courses?.length || 0}</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="stat-card">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <i data-feather="play-circle" class="h-8 w-8 text-purple-500"></i>
                                </div>
                                <div class="ml-4">
                                    <p class="stat-label">Active Sessions</p>
                                    <p class="stat-value">--</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="stat-card">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <i data-feather="trending-up" class="h-8 w-8 text-orange-500"></i>
                                </div>
                                <div class="ml-4">
                                    <p class="stat-label">Growth</p>
                                    <p class="stat-value">+12%</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Activity -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div class="card">
                            <div class="card-header">
                                <h3 class="text-lg font-medium text-gray-900">Recent Users</h3>
                            </div>
                            <div class="card-body">
                                ${this.renderUsersList(usersData.data?.users || [])}
                            </div>
                        </div>
                        
                        <div class="card">
                            <div class="card-header">
                                <h3 class="text-lg font-medium text-gray-900">Recent Courses</h3>
                            </div>
                            <div class="card-body">
                                ${this.renderCoursesList(coursesData.data?.courses || [])}
                            </div>
                        </div>
                    </div>
                </div>
            `;

            container.innerHTML = html;
            feather.replace();

        } catch (error) {
            console.error('Admin dashboard error:', error);
            this.showError('Failed to load admin dashboard');
        }
    }

    async loadInstructorDashboard() {
        const container = document.getElementById('dashboard-container');
        
        try {
            // Fetch instructor's courses
            const coursesData = await window.api.getCourses({ 
                instructor: this.currentUser.id,
                limit: 10 
            });

            const html = `
                <div class="p-6">
                    <div class="mb-8">
                        <h1 class="text-3xl font-bold text-gray-900">Instructor Dashboard</h1>
                        <p class="text-gray-600">Welcome back, ${this.currentUser.firstName}!</p>
                    </div>

                    <!-- Quick Actions -->
                    <div class="mb-8">
                        <div class="flex flex-wrap gap-4">
                            <button class="btn btn-primary" onclick="createNewCourse()">
                                <i data-feather="plus" class="h-4 w-4 mr-2"></i>
                                Create Course
                            </button>
                            <button class="btn btn-secondary" onclick="uploadVideo()">
                                <i data-feather="upload" class="h-4 w-4 mr-2"></i>
                                Upload Video
                            </button>
                            <button class="btn btn-secondary" onclick="createQuiz()">
                                <i data-feather="help-circle" class="h-4 w-4 mr-2"></i>
                                Create Quiz
                            </button>
                        </div>
                    </div>

                    <!-- Statistics -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                        <div class="stat-card">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <i data-feather="book" class="h-8 w-8 text-blue-500"></i>
                                </div>
                                <div class="ml-4">
                                    <p class="stat-label">My Courses</p>
                                    <p class="stat-value">${coursesData.data?.courses?.length || 0}</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="stat-card">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <i data-feather="users" class="h-8 w-8 text-green-500"></i>
                                </div>
                                <div class="ml-4">
                                    <p class="stat-label">Total Students</p>
                                    <p class="stat-value">--</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="stat-card">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <i data-feather="star" class="h-8 w-8 text-yellow-500"></i>
                                </div>
                                <div class="ml-4">
                                    <p class="stat-label">Avg Rating</p>
                                    <p class="stat-value">4.8</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- My Courses -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="text-lg font-medium text-gray-900">My Courses</h3>
                        </div>
                        <div class="card-body">
                            ${this.renderInstructorCourses(coursesData.data?.courses || [])}
                        </div>
                    </div>
                </div>
            `;

            container.innerHTML = html;
            feather.replace();

        } catch (error) {
            console.error('Instructor dashboard error:', error);
            this.showError('Failed to load instructor dashboard');
        }
    }

    async loadStudentDashboard() {
        const container = document.getElementById('dashboard-container');
        
        try {
            // Fetch student's enrolled courses
            const coursesData = await window.api.getCourses({ 
                student: this.currentUser.id,
                limit: 10 
            });

            const html = `
                <div class="p-6">
                    <div class="mb-8">
                        <h1 class="text-3xl font-bold text-gray-900">Student Dashboard</h1>
                        <p class="text-gray-600">Welcome back, ${this.currentUser.firstName}!</p>
                    </div>

                    <!-- Progress Overview -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                        <div class="stat-card">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <i data-feather="book-open" class="h-8 w-8 text-blue-500"></i>
                                </div>
                                <div class="ml-4">
                                    <p class="stat-label">Enrolled Courses</p>
                                    <p class="stat-value">${coursesData.data?.courses?.length || 0}</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="stat-card">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <i data-feather="check-circle" class="h-8 w-8 text-green-500"></i>
                                </div>
                                <div class="ml-4">
                                    <p class="stat-label">Completed</p>
                                    <p class="stat-value">--</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="stat-card">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <i data-feather="award" class="h-8 w-8 text-yellow-500"></i>
                                </div>
                                <div class="ml-4">
                                    <p class="stat-label">Certificates</p>
                                    <p class="stat-value">--</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Continue Learning -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="text-lg font-medium text-gray-900">Continue Learning</h3>
                        </div>
                        <div class="card-body">
                            ${this.renderStudentCourses(coursesData.data?.courses || [])}
                        </div>
                    </div>
                </div>
            `;

            container.innerHTML = html;
            feather.replace();

        } catch (error) {
            console.error('Student dashboard error:', error);
            this.showError('Failed to load student dashboard');
        }
    }

    renderUsersList(users) {
        if (!users.length) {
            return '<p class="text-gray-500 text-center py-4">No users found</p>';
        }

        return `
            <div class="space-y-3">
                ${users.map(user => `
                    <div class="flex items-center space-x-3">
                        <img class="h-8 w-8 rounded-full" src="${user.avatarUrl || 'https://via.placeholder.com/32'}" alt="${user.firstName}">
                        <div class="flex-1 min-w-0">
                            <p class="text-sm font-medium text-gray-900 truncate">${user.firstName} ${user.lastName}</p>
                            <p class="text-sm text-gray-500 truncate">${user.email}</p>
                        </div>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-${user.role === 'admin' ? 'red' : user.role === 'instructor' ? 'blue' : 'green'}-100 text-${user.role === 'admin' ? 'red' : user.role === 'instructor' ? 'blue' : 'green'}-800">
                            ${user.role}
                        </span>
                    </div>
                `).join('')}
            </div>
        `;
    }

    renderCoursesList(courses) {
        if (!courses.length) {
            return '<p class="text-gray-500 text-center py-4">No courses found</p>';
        }

        return `
            <div class="space-y-3">
                ${courses.map(course => `
                    <div class="flex items-center space-x-3">
                        <img class="h-12 w-12 rounded object-cover" src="${course.thumbnailUrl || 'https://via.placeholder.com/48'}" alt="${course.title}">
                        <div class="flex-1 min-w-0">
                            <p class="text-sm font-medium text-gray-900 truncate">${course.title}</p>
                            <p class="text-sm text-gray-500 truncate">${course.instructor?.firstName} ${course.instructor?.lastName}</p>
                        </div>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${course.isPublished ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}">
                            ${course.isPublished ? 'Published' : 'Draft'}
                        </span>
                    </div>
                `).join('')}
            </div>
        `;
    }

    renderInstructorCourses(courses) {
        if (!courses.length) {
            return `
                <div class="text-center py-8">
                    <i data-feather="book" class="h-12 w-12 text-gray-400 mx-auto mb-4"></i>
                    <p class="text-gray-500">No courses yet</p>
                    <button class="btn btn-primary mt-4" onclick="createNewCourse()">Create Your First Course</button>
                </div>
            `;
        }

        return `
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                ${courses.map(course => `
                    <div class="course-card">
                        <img class="course-thumbnail" src="${course.thumbnailUrl || 'https://via.placeholder.com/300x200'}" alt="${course.title}">
                        <div class="p-4">
                            <h4 class="font-medium text-gray-900 mb-2">${course.title}</h4>
                            <p class="text-sm text-gray-600 mb-3">${truncateText(course.description || '', 100)}</p>
                            <div class="flex items-center justify-between">
                                <span class="course-badge course-badge-${course.difficultyLevel || 'beginner'}">${course.difficultyLevel || 'Beginner'}</span>
                                <button class="btn btn-sm btn-primary" onclick="editCourse('${course.id}')">Edit</button>
                            </div>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
    }

    renderStudentCourses(courses) {
        if (!courses.length) {
            return `
                <div class="text-center py-8">
                    <i data-feather="book-open" class="h-12 w-12 text-gray-400 mx-auto mb-4"></i>
                    <p class="text-gray-500">No enrolled courses</p>
                    <button class="btn btn-primary mt-4" onclick="browseCourses()">Browse Courses</button>
                </div>
            `;
        }

        return `
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                ${courses.map(course => `
                    <div class="course-card">
                        <img class="course-thumbnail" src="${course.thumbnailUrl || 'https://via.placeholder.com/300x200'}" alt="${course.title}">
                        <div class="p-4">
                            <h4 class="font-medium text-gray-900 mb-2">${course.title}</h4>
                            <p class="text-sm text-gray-600 mb-3">${truncateText(course.description || '', 100)}</p>
                            <div class="mb-3">
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: ${course.progress || 0}%"></div>
                                </div>
                                <p class="text-xs text-gray-500 mt-1">${course.progress || 0}% complete</p>
                            </div>
                            <button class="btn btn-sm btn-primary w-full" onclick="continueCourse('${course.id}')">Continue Learning</button>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
    }

    getLoadingHTML() {
        return `
            <div class="flex items-center justify-center h-64">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
            </div>
        `;
    }

    showError(message) {
        const container = document.getElementById('dashboard-container');
        container.innerHTML = `
            <div class="text-center py-12">
                <i data-feather="alert-circle" class="h-12 w-12 text-red-500 mx-auto mb-4"></i>
                <h2 class="text-xl font-bold text-gray-900 mb-2">Error</h2>
                <p class="text-gray-600">${message}</p>
                <button class="btn btn-primary mt-4" onclick="window.dashboardManager.loadDashboard()">Try Again</button>
            </div>
        `;
        feather.replace();
    }
}

// Initialize dashboard manager
window.dashboardManager = new DashboardManager();

// Global functions for dashboard actions
window.createNewCourse = function() {
    showToast('Create course functionality coming soon!', 'info');
};

window.uploadVideo = function() {
    showToast('Upload video functionality coming soon!', 'info');
};

window.createQuiz = function() {
    showToast('Create quiz functionality coming soon!', 'info');
};

window.editCourse = function(courseId) {
    showToast(`Edit course ${courseId} functionality coming soon!`, 'info');
};

window.continueCourse = function(courseId) {
    showToast(`Continue course ${courseId} functionality coming soon!`, 'info');
};

window.browseCourses = function() {
    navigateTo('courses');
};
