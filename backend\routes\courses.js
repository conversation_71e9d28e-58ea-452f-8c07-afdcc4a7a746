const express = require('express');
const { body, param, query } = require('express-validator');
const CourseController = require('../controllers/courseController');
const {
  verifyFirebaseToken,
  requireAuthenticated,
  requireInstructorOrAdmin,
  requireAdmin,
  optionalAuth
} = require('../middleware/auth');

const router = express.Router();

// Validation rules
const createCourseValidation = [
  body('title')
    .trim()
    .isLength({ min: 3, max: 255 })
    .withMessage('Title must be between 3 and 255 characters'),
  body('description')
    .optional()
    .isLength({ max: 2000 })
    .withMessage('Description must not exceed 2000 characters'),
  body('category')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Category must not exceed 100 characters'),
  body('difficultyLevel')
    .optional()
    .isIn(['beginner', 'intermediate', 'advanced'])
    .withMessage('Difficulty level must be beginner, intermediate, or advanced'),
  body('estimatedDuration')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Estimated duration must be a positive integer (minutes)'),
  body('price')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Price must be a positive number'),
  body('thumbnailUrl')
    .optional()
    .isURL()
    .withMessage('Thumbnail URL must be a valid URL')
];

const updateCourseValidation = [
  body('title')
    .optional()
    .trim()
    .isLength({ min: 3, max: 255 })
    .withMessage('Title must be between 3 and 255 characters'),
  body('description')
    .optional()
    .isLength({ max: 2000 })
    .withMessage('Description must not exceed 2000 characters'),
  body('category')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Category must not exceed 100 characters'),
  body('difficultyLevel')
    .optional()
    .isIn(['beginner', 'intermediate', 'advanced'])
    .withMessage('Difficulty level must be beginner, intermediate, or advanced'),
  body('estimatedDuration')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Estimated duration must be a positive integer (minutes)'),
  body('price')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Price must be a positive number'),
  body('thumbnailUrl')
    .optional()
    .isURL()
    .withMessage('Thumbnail URL must be a valid URL'),
  body('isPublished')
    .optional()
    .isBoolean()
    .withMessage('isPublished must be a boolean')
];

const enrollStudentValidation = [
  body('studentId')
    .isUUID()
    .withMessage('Student ID must be a valid UUID')
];

const updateProgressValidation = [
  body('progressPercentage')
    .optional()
    .isInt({ min: 0, max: 100 })
    .withMessage('Progress percentage must be between 0 and 100'),
  body('lastAccessedVideoId')
    .optional()
    .isUUID()
    .withMessage('Last accessed video ID must be a valid UUID'),
  body('completedAt')
    .optional()
    .isISO8601()
    .withMessage('Completed at must be a valid ISO 8601 date')
];

const idValidation = [
  param('id')
    .isUUID()
    .withMessage('Course ID must be a valid UUID')
];

const queryValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  query('published')
    .optional()
    .isBoolean()
    .withMessage('Published must be a boolean')
];

// Routes

/**
 * @route   GET /api/courses
 * @desc    Get all courses with optional filtering
 * @access  Public (published courses) / Private (all courses for instructors/admins)
 */
router.get('/',
  optionalAuth,
  queryValidation,
  CourseController.getAllCourses
);

/**
 * @route   GET /api/courses/:id
 * @desc    Get course by ID
 * @access  Public (published courses) / Private (unpublished courses for owners/admins)
 */
router.get('/:id',
  optionalAuth,
  idValidation,
  CourseController.getCourseById
);

/**
 * @route   POST /api/courses
 * @desc    Create new course
 * @access  Private (Instructor/Admin)
 */
router.post('/',
  verifyFirebaseToken,
  requireInstructorOrAdmin,
  createCourseValidation,
  CourseController.createCourse
);

/**
 * @route   PUT /api/courses/:id
 * @desc    Update course
 * @access  Private (Course owner/Admin)
 */
router.put('/:id',
  verifyFirebaseToken,
  requireInstructorOrAdmin,
  idValidation,
  updateCourseValidation,
  CourseController.updateCourse
);

/**
 * @route   DELETE /api/courses/:id
 * @desc    Delete course
 * @access  Private (Course owner/Admin)
 */
router.delete('/:id',
  verifyFirebaseToken,
  requireInstructorOrAdmin,
  idValidation,
  CourseController.deleteCourse
);

/**
 * @route   GET /api/courses/:id/enrollments
 * @desc    Get course enrollments
 * @access  Private (Course owner/Admin)
 */
router.get('/:id/enrollments',
  verifyFirebaseToken,
  requireInstructorOrAdmin,
  idValidation,
  CourseController.getCourseEnrollments
);

/**
 * @route   POST /api/courses/:id/enroll
 * @desc    Enroll student in course
 * @access  Private (Course owner/Admin)
 */
router.post('/:id/enroll',
  verifyFirebaseToken,
  requireInstructorOrAdmin,
  idValidation,
  enrollStudentValidation,
  CourseController.enrollStudent
);

/**
 * @route   DELETE /api/courses/:id/students/:studentId
 * @desc    Unenroll student from course
 * @access  Private (Course owner/Admin)
 */
router.delete('/:id/students/:studentId',
  verifyFirebaseToken,
  requireInstructorOrAdmin,
  [
    param('id').isUUID().withMessage('Course ID must be a valid UUID'),
    param('studentId').isUUID().withMessage('Student ID must be a valid UUID')
  ],
  CourseController.unenrollStudent
);

/**
 * @route   GET /api/courses/:id/progress
 * @desc    Get student's course progress
 * @access  Private (Student)
 */
router.get('/:id/progress',
  verifyFirebaseToken,
  requireAuthenticated,
  idValidation,
  CourseController.getCourseProgress
);

/**
 * @route   PUT /api/courses/:id/progress
 * @desc    Update student's course progress
 * @access  Private (Student)
 */
router.put('/:id/progress',
  verifyFirebaseToken,
  requireAuthenticated,
  idValidation,
  updateProgressValidation,
  CourseController.updateCourseProgress
);

module.exports = router;
