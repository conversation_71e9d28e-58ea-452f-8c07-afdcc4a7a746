// Main application entry point
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Application initializing...');
    
    // Initialize the application
    initializeApp();
});

function initializeApp() {
    try {
        // Set up global error handling
        setupErrorHandling();
        
        // Initialize UI components
        initializeUI();
        
        // Set up event listeners
        setupEventListeners();
        
        // Initialize authentication
        if (window.authManager) {
            console.log('✅ Authentication manager initialized');
        }
        
        console.log('✅ Application initialized successfully');
    } catch (error) {
        console.error('❌ Application initialization failed:', error);
        showToast('Application failed to initialize. Please refresh the page.', 'error');
    }
}

function setupErrorHandling() {
    // Global error handler for unhandled errors
    window.addEventListener('error', (event) => {
        console.error('Global error:', event.error);
        if (window.DEBUG) {
            showToast(`Error: ${event.error.message}`, 'error');
        } else {
            showToast('An unexpected error occurred. Please try again.', 'error');
        }
    });

    // Global handler for unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
        console.error('Unhandled promise rejection:', event.reason);
        if (window.DEBUG) {
            showToast(`Promise rejection: ${event.reason}`, 'error');
        } else {
            showToast('An unexpected error occurred. Please try again.', 'error');
        }
        event.preventDefault();
    });
}

function initializeUI() {
    // Initialize Feather icons
    if (typeof feather !== 'undefined') {
        feather.replace();
    }
    
    // Set up responsive navigation
    setupResponsiveNavigation();
    
    // Initialize tooltips and other UI components
    initializeTooltips();
    
    // Set up keyboard shortcuts
    setupKeyboardShortcuts();
}

function setupEventListeners() {
    // Authentication form listeners
    setupAuthFormListeners();
    
    // Navigation listeners
    setupNavigationListeners();
    
    // User menu listeners
    setupUserMenuListeners();
    
    // Global listeners
    setupGlobalListeners();
}

function setupAuthFormListeners() {
    // Login form
    const loginForm = document.getElementById('login-form');
    if (loginForm) {
        loginForm.addEventListener('submit', handleLogin);
    }
    
    // Register form
    const registerForm = document.getElementById('register-form');
    if (registerForm) {
        registerForm.addEventListener('submit', handleRegister);
    }
    
    // Show register form
    const showRegisterLink = document.getElementById('show-register');
    if (showRegisterLink) {
        showRegisterLink.addEventListener('click', (e) => {
            e.preventDefault();
            toggleAuthForms('register');
        });
    }
    
    // Show login form
    const showLoginLink = document.getElementById('show-login');
    if (showLoginLink) {
        showLoginLink.addEventListener('click', (e) => {
            e.preventDefault();
            toggleAuthForms('login');
        });
    }
    
    // Forgot password
    const forgotPasswordLink = document.getElementById('forgot-password-link');
    if (forgotPasswordLink) {
        forgotPasswordLink.addEventListener('click', handleForgotPassword);
    }
}

function setupNavigationListeners() {
    // Dashboard navigation
    const navDashboard = document.getElementById('nav-dashboard');
    if (navDashboard) {
        navDashboard.addEventListener('click', (e) => {
            e.preventDefault();
            navigateTo('dashboard');
        });
    }
    
    // Courses navigation
    const navCourses = document.getElementById('nav-courses');
    if (navCourses) {
        navCourses.addEventListener('click', (e) => {
            e.preventDefault();
            navigateTo('courses');
        });
    }
    
    // Quizzes navigation
    const navQuizzes = document.getElementById('nav-quizzes');
    if (navQuizzes) {
        navQuizzes.addEventListener('click', (e) => {
            e.preventDefault();
            navigateTo('quizzes');
        });
    }
    
    // Students navigation
    const navStudents = document.getElementById('nav-students');
    if (navStudents) {
        navStudents.addEventListener('click', (e) => {
            e.preventDefault();
            navigateTo('students');
        });
    }
}

function setupUserMenuListeners() {
    // User menu toggle
    const userMenuButton = document.getElementById('user-menu-button');
    const userMenu = document.getElementById('user-menu');
    
    if (userMenuButton && userMenu) {
        userMenuButton.addEventListener('click', (e) => {
            e.stopPropagation();
            userMenu.classList.toggle('hidden');
        });
        
        // Close menu when clicking outside
        document.addEventListener('click', () => {
            userMenu.classList.add('hidden');
        });
    }
    
    // Profile menu item
    const menuProfile = document.getElementById('menu-profile');
    if (menuProfile) {
        menuProfile.addEventListener('click', (e) => {
            e.preventDefault();
            navigateTo('profile');
        });
    }
    
    // Settings menu item
    const menuSettings = document.getElementById('menu-settings');
    if (menuSettings) {
        menuSettings.addEventListener('click', (e) => {
            e.preventDefault();
            navigateTo('settings');
        });
    }
    
    // Logout menu item
    const menuLogout = document.getElementById('menu-logout');
    if (menuLogout) {
        menuLogout.addEventListener('click', (e) => {
            e.preventDefault();
            handleLogout();
        });
    }
}

function setupGlobalListeners() {
    // Handle browser back/forward buttons
    window.addEventListener('popstate', (event) => {
        if (event.state && event.state.page) {
            navigateTo(event.state.page, false);
        }
    });
    
    // Handle online/offline status
    window.addEventListener('online', () => {
        showToast('Connection restored', 'success');
    });
    
    window.addEventListener('offline', () => {
        showToast('Connection lost. Some features may not work.', 'warning');
    });
    
    // Handle visibility change (tab switching)
    document.addEventListener('visibilitychange', () => {
        if (document.visibilityState === 'visible') {
            // Tab became visible - refresh data if needed
            refreshCurrentView();
        }
    });
}

function setupResponsiveNavigation() {
    // Mobile menu toggle (if implemented)
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const mobileMenu = document.getElementById('mobile-menu');
    
    if (mobileMenuButton && mobileMenu) {
        mobileMenuButton.addEventListener('click', () => {
            mobileMenu.classList.toggle('hidden');
        });
    }
}

function initializeTooltips() {
    // Initialize tooltips for elements with data-tooltip attribute
    const tooltipElements = document.querySelectorAll('[data-tooltip]');
    tooltipElements.forEach(element => {
        element.addEventListener('mouseenter', showTooltip);
        element.addEventListener('mouseleave', hideTooltip);
    });
}

function setupKeyboardShortcuts() {
    document.addEventListener('keydown', (e) => {
        // Ctrl/Cmd + K for search
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            openSearch();
        }
        
        // Escape to close modals
        if (e.key === 'Escape') {
            closeAllModals();
        }
        
        // Ctrl/Cmd + Enter to submit forms
        if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
            const activeForm = document.activeElement.closest('form');
            if (activeForm) {
                activeForm.dispatchEvent(new Event('submit'));
            }
        }
    });
}

// Authentication handlers
async function handleLogin(e) {
    e.preventDefault();
    
    const form = e.target;
    if (!validateForm(form)) {
        return;
    }
    
    const formData = new FormData(form);
    const email = formData.get('email');
    const password = formData.get('password');
    
    const success = await window.authManager.login(email, password);
    if (success) {
        form.reset();
    }
}

async function handleRegister(e) {
    e.preventDefault();
    
    const form = e.target;
    if (!validateForm(form)) {
        return;
    }
    
    const formData = new FormData(form);
    const userData = {
        email: formData.get('email'),
        password: formData.get('password'),
        firstName: formData.get('firstName'),
        lastName: formData.get('lastName'),
        role: formData.get('role')
    };
    
    const success = await window.authManager.register(userData);
    if (success) {
        form.reset();
    }
}

async function handleLogout() {
    const confirmed = confirm('Are you sure you want to log out?');
    if (confirmed) {
        await window.authManager.logout();
    }
}

async function handleForgotPassword(e) {
    e.preventDefault();
    
    const email = prompt('Please enter your email address:');
    if (email && validateEmail(email)) {
        await window.authManager.forgotPassword(email);
    } else if (email) {
        showToast('Please enter a valid email address', 'error');
    }
}

// Navigation functions
function toggleAuthForms(formType) {
    const loginForm = document.getElementById('login-form');
    const registerForm = document.getElementById('register-form');
    
    if (formType === 'register') {
        loginForm.classList.add('hidden');
        registerForm.classList.remove('hidden');
    } else {
        registerForm.classList.add('hidden');
        loginForm.classList.remove('hidden');
    }
}

function navigateTo(page, pushState = true) {
    // Update navigation active state
    updateNavigationState(page);
    
    // Load page content
    loadPageContent(page);
    
    // Update browser history
    if (pushState) {
        history.pushState({ page }, '', `#${page}`);
    }
}

function updateNavigationState(activePage) {
    const navItems = document.querySelectorAll('[id^="nav-"]');
    navItems.forEach(item => {
        item.classList.remove('text-gray-900');
        item.classList.add('text-gray-500');
    });
    
    const activeNavItem = document.getElementById(`nav-${activePage}`);
    if (activeNavItem) {
        activeNavItem.classList.remove('text-gray-500');
        activeNavItem.classList.add('text-gray-900');
    }
}

function loadPageContent(page) {
    const dashboardContainer = document.getElementById('dashboard-container');
    if (!dashboardContainer) return;
    
    // Show loading state
    dashboardContainer.innerHTML = '<div class="flex items-center justify-center h-64"><div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div></div>';
    
    // Load content based on page
    switch (page) {
        case 'dashboard':
            if (window.dashboardManager) {
                window.dashboardManager.loadDashboard();
            }
            break;
        case 'courses':
            if (window.coursesManager) {
                window.coursesManager.loadCourses();
            }
            break;
        case 'quizzes':
            if (window.quizzesManager) {
                window.quizzesManager.loadQuizzes();
            }
            break;
        case 'students':
            if (window.usersManager) {
                window.usersManager.loadStudents();
            }
            break;
        case 'profile':
            loadProfilePage();
            break;
        case 'settings':
            loadSettingsPage();
            break;
        default:
            dashboardContainer.innerHTML = '<div class="text-center py-12"><h2 class="text-2xl font-bold text-gray-900">Page not found</h2></div>';
    }
}

function refreshCurrentView() {
    // Refresh current page data if user is authenticated
    if (window.authManager && window.authManager.isAuthenticated) {
        const currentPage = getCurrentPage();
        if (currentPage) {
            loadPageContent(currentPage);
        }
    }
}

function getCurrentPage() {
    const hash = window.location.hash.substring(1);
    return hash || 'dashboard';
}

// Utility UI functions
function openSearch() {
    // Implement search functionality
    console.log('Search opened');
}

function closeAllModals() {
    const modals = document.querySelectorAll('#modals-container > div');
    modals.forEach(modal => {
        modal.style.opacity = '0';
        setTimeout(() => {
            if (modal.parentNode) {
                modal.parentNode.removeChild(modal);
            }
        }, 300);
    });
}

function showTooltip(e) {
    const element = e.target;
    const tooltipText = element.dataset.tooltip;
    
    if (!tooltipText) return;
    
    const tooltip = document.createElement('div');
    tooltip.className = 'absolute z-50 px-2 py-1 text-sm text-white bg-gray-900 rounded shadow-lg tooltip';
    tooltip.textContent = tooltipText;
    
    document.body.appendChild(tooltip);
    
    const rect = element.getBoundingClientRect();
    tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
    tooltip.style.top = rect.top - tooltip.offsetHeight - 5 + 'px';
}

function hideTooltip() {
    const tooltips = document.querySelectorAll('.tooltip');
    tooltips.forEach(tooltip => tooltip.remove());
}

function loadProfilePage() {
    // Implement profile page loading
    const dashboardContainer = document.getElementById('dashboard-container');
    dashboardContainer.innerHTML = '<div class="p-6"><h2 class="text-2xl font-bold text-gray-900">Profile</h2><p class="text-gray-600">Profile page coming soon...</p></div>';
}

function loadSettingsPage() {
    // Implement settings page loading
    const dashboardContainer = document.getElementById('dashboard-container');
    dashboardContainer.innerHTML = '<div class="p-6"><h2 class="text-2xl font-bold text-gray-900">Settings</h2><p class="text-gray-600">Settings page coming soon...</p></div>';
}

// Initialize page based on URL hash
window.addEventListener('load', () => {
    const currentPage = getCurrentPage();
    if (currentPage && window.authManager && window.authManager.isAuthenticated) {
        navigateTo(currentPage, false);
    }
});

console.log('✅ Main application script loaded');
