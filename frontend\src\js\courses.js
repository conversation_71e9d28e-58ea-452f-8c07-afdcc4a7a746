// وحدة إدارة الدورات
class CoursesManager {
    constructor() {
        this.currentCourse = null;
        this.currentSection = null;
        this.currentVideo = null;
        this.videoPlayer = null;
        this.courses = [];
        this.userRole = null;
    }

    // تحميل صفحة الدورات
    async loadCourses() {
        try {
            this.userRole = window.authManager.currentUser?.role;
            const container = document.getElementById('dashboard-container');
            
            // إظهار حالة التحميل
            container.innerHTML = this.getLoadingHTML();

            // جلب الدورات
            const response = await window.api.getCourses({
                instructor: this.userRole === 'instructor' ? window.authManager.currentUser.id : undefined,
                published: this.userRole === 'student' ? true : undefined
            });

            this.courses = response.data.courses;

            // عرض الدورات
            this.renderCoursesPage();

        } catch (error) {
            console.error('خطأ في تحميل الدورات:', error);
            this.showError('فشل في تحميل الدورات');
        }
    }

    // عرض صفحة الدورات
    renderCoursesPage() {
        const container = document.getElementById('dashboard-container');
        
        const html = `
            <div class="p-6">
                <div class="flex justify-between items-center mb-8">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">الدورات التدريبية</h1>
                        <p class="text-gray-600">استكشف وتعلم من مجموعة واسعة من الدورات</p>
                    </div>
                    ${this.userRole === 'instructor' || this.userRole === 'admin' ? `
                        <button class="btn btn-primary" onclick="coursesManager.showCreateCourseModal()">
                            <i data-feather="plus" class="h-4 w-4 mr-2"></i>
                            إنشاء دورة جديدة
                        </button>
                    ` : ''}
                </div>

                <!-- فلاتر البحث -->
                <div class="mb-6">
                    <div class="flex flex-wrap gap-4">
                        <div class="flex-1 min-w-64">
                            <input type="text" id="course-search" placeholder="البحث في الدورات..." 
                                   class="form-input w-full" onkeyup="coursesManager.searchCourses(this.value)">
                        </div>
                        <select id="category-filter" class="form-input" onchange="coursesManager.filterByCategory(this.value)">
                            <option value="">جميع الفئات</option>
                            <option value="programming">البرمجة</option>
                            <option value="design">التصميم</option>
                            <option value="business">الأعمال</option>
                            <option value="marketing">التسويق</option>
                        </select>
                        <select id="level-filter" class="form-input" onchange="coursesManager.filterByLevel(this.value)">
                            <option value="">جميع المستويات</option>
                            <option value="beginner">مبتدئ</option>
                            <option value="intermediate">متوسط</option>
                            <option value="advanced">متقدم</option>
                        </select>
                    </div>
                </div>

                <!-- قائمة الدورات -->
                <div id="courses-grid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                    ${this.renderCoursesGrid()}
                </div>

                ${this.courses.length === 0 ? this.renderEmptyState() : ''}
            </div>
        `;

        container.innerHTML = html;
        feather.replace();
    }

    // عرض شبكة الدورات
    renderCoursesGrid() {
        return this.courses.map(course => `
            <div class="course-card cursor-pointer" onclick="coursesManager.openCourse('${course.id}')">
                <img class="course-thumbnail" src="${course.thumbnailUrl || 'https://via.placeholder.com/300x200?text=دورة+تدريبية'}" alt="${course.title}">
                <div class="p-4">
                    <div class="flex items-center justify-between mb-2">
                        <span class="course-badge course-badge-${course.difficultyLevel || 'beginner'}">${this.getLevelText(course.difficultyLevel)}</span>
                        ${course.price > 0 ? `<span class="text-primary-600 font-bold">${course.price} ر.س</span>` : '<span class="text-green-600 font-bold">مجاني</span>'}
                    </div>
                    <h3 class="font-bold text-gray-900 mb-2 line-clamp-2">${course.title}</h3>
                    <p class="text-gray-600 text-sm mb-3 line-clamp-3">${course.description || 'لا يوجد وصف متاح'}</p>
                    
                    <div class="flex items-center justify-between text-sm text-gray-500 mb-3">
                        <div class="flex items-center">
                            <i data-feather="user" class="h-4 w-4 mr-1"></i>
                            <span>${course.instructor?.firstName} ${course.instructor?.lastName}</span>
                        </div>
                        <div class="flex items-center">
                            <i data-feather="clock" class="h-4 w-4 mr-1"></i>
                            <span>${this.formatDuration(course.estimatedDuration)}</span>
                        </div>
                    </div>

                    ${this.userRole === 'student' && course.progress !== undefined ? `
                        <div class="mb-3">
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: ${course.progress}%"></div>
                            </div>
                            <p class="text-xs text-gray-500 mt-1">${course.progress}% مكتمل</p>
                        </div>
                    ` : ''}

                    <div class="flex gap-2">
                        ${this.userRole === 'student' ? `
                            <button class="btn btn-primary btn-sm flex-1" onclick="event.stopPropagation(); coursesManager.enrollInCourse('${course.id}')">
                                ${course.isEnrolled ? 'متابعة التعلم' : 'التسجيل في الدورة'}
                            </button>
                        ` : ''}
                        
                        ${(this.userRole === 'instructor' && course.instructorId === window.authManager.currentUser.id) || this.userRole === 'admin' ? `
                            <button class="btn btn-secondary btn-sm" onclick="event.stopPropagation(); coursesManager.editCourse('${course.id}')">
                                <i data-feather="edit" class="h-4 w-4"></i>
                            </button>
                            <button class="btn btn-danger btn-sm" onclick="event.stopPropagation(); coursesManager.deleteCourse('${course.id}')">
                                <i data-feather="trash-2" class="h-4 w-4"></i>
                            </button>
                        ` : ''}
                    </div>
                </div>
            </div>
        `).join('');
    }

    // عرض حالة فارغة
    renderEmptyState() {
        return `
            <div class="col-span-full text-center py-12">
                <i data-feather="book-open" class="h-16 w-16 text-gray-400 mx-auto mb-4"></i>
                <h3 class="text-xl font-medium text-gray-900 mb-2">لا توجد دورات</h3>
                <p class="text-gray-600 mb-4">
                    ${this.userRole === 'instructor' ? 'ابدأ بإنشاء دورتك الأولى' : 'لا توجد دورات متاحة حالياً'}
                </p>
                ${this.userRole === 'instructor' || this.userRole === 'admin' ? `
                    <button class="btn btn-primary" onclick="coursesManager.showCreateCourseModal()">
                        إنشاء دورة جديدة
                    </button>
                ` : ''}
            </div>
        `;
    }

    // فتح دورة
    async openCourse(courseId) {
        try {
            showLoading();
            
            const response = await window.api.getCourse(courseId);
            this.currentCourse = response.data.course;
            
            this.renderCourseView();
            
        } catch (error) {
            console.error('خطأ في فتح الدورة:', error);
            showToast('فشل في تحميل الدورة', 'error');
        } finally {
            hideLoading();
        }
    }

    // عرض صفحة الدورة
    renderCourseView() {
        const container = document.getElementById('dashboard-container');
        
        const html = `
            <div class="flex h-screen">
                <!-- قائمة المحتوى -->
                <div class="w-80 bg-white border-r border-gray-200 overflow-y-auto">
                    <div class="p-4 border-b border-gray-200">
                        <button class="btn btn-secondary btn-sm mb-3" onclick="coursesManager.loadCourses()">
                            <i data-feather="arrow-right" class="h-4 w-4 mr-2"></i>
                            العودة للدورات
                        </button>
                        <h2 class="font-bold text-lg text-gray-900">${this.currentCourse.title}</h2>
                        <p class="text-sm text-gray-600">${this.currentCourse.instructor?.firstName} ${this.currentCourse.instructor?.lastName}</p>
                    </div>
                    
                    <div class="course-content">
                        ${this.renderCourseSections()}
                    </div>
                </div>

                <!-- منطقة المحتوى الرئيسي -->
                <div class="flex-1 flex flex-col">
                    <!-- مشغل الفيديو -->
                    <div class="flex-1 bg-black">
                        <div id="video-player-container" class="h-full">
                            ${this.currentVideo ? '' : this.renderWelcomeScreen()}
                        </div>
                    </div>

                    <!-- معلومات الفيديو والمواد -->
                    <div class="h-64 bg-white border-t border-gray-200 overflow-y-auto">
                        <div id="video-info-container" class="p-4">
                            ${this.currentVideo ? this.renderVideoInfo() : this.renderCourseInfo()}
                        </div>
                    </div>
                </div>
            </div>
        `;

        container.innerHTML = html;
        feather.replace();

        // تهيئة مشغل الفيديو إذا كان هناك فيديو محدد
        if (this.currentVideo) {
            this.initVideoPlayer();
        }
    }

    // عرض أقسام الدورة
    renderCourseSections() {
        if (!this.currentCourse.sections || this.currentCourse.sections.length === 0) {
            return '<div class="p-4 text-center text-gray-500">لا يوجد محتوى متاح</div>';
        }

        return this.currentCourse.sections.map(section => `
            <div class="section-item">
                <div class="section-header p-4 bg-gray-50 border-b border-gray-200">
                    <h3 class="font-medium text-gray-900">${section.title}</h3>
                    <p class="text-sm text-gray-600">${section.videos?.length || 0} فيديو</p>
                </div>
                <div class="section-videos">
                    ${section.videos?.map(video => `
                        <div class="video-item p-3 border-b border-gray-100 cursor-pointer hover:bg-gray-50 ${this.currentVideo?.id === video.id ? 'bg-primary-50 border-primary-200' : ''}" 
                             onclick="coursesManager.playVideo('${video.id}')">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 mr-3">
                                    <div class="w-8 h-8 bg-gray-200 rounded flex items-center justify-center">
                                        <i data-feather="${video.isCompleted ? 'check-circle' : 'play-circle'}" 
                                           class="h-4 w-4 ${video.isCompleted ? 'text-green-500' : 'text-gray-500'}"></i>
                                    </div>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm font-medium text-gray-900 truncate">${video.title}</p>
                                    <p class="text-xs text-gray-500">${this.formatDuration(video.duration)}</p>
                                </div>
                                ${video.isPreview ? '<span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">معاينة</span>' : ''}
                            </div>
                        </div>
                    `).join('') || '<div class="p-3 text-sm text-gray-500">لا توجد فيديوهات</div>'}
                </div>
            </div>
        `).join('');
    }

    // شاشة الترحيب
    renderWelcomeScreen() {
        return `
            <div class="h-full flex items-center justify-center bg-gradient-to-br from-primary-600 to-primary-800 text-white">
                <div class="text-center">
                    <i data-feather="play-circle" class="h-24 w-24 mx-auto mb-6 opacity-80"></i>
                    <h2 class="text-3xl font-bold mb-4">مرحباً بك في ${this.currentCourse.title}</h2>
                    <p class="text-xl opacity-90 mb-6">اختر فيديو من القائمة لبدء التعلم</p>
                    <div class="flex items-center justify-center space-x-6 rtl:space-x-reverse">
                        <div class="text-center">
                            <div class="text-2xl font-bold">${this.currentCourse.sections?.length || 0}</div>
                            <div class="text-sm opacity-75">قسم</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold">${this.getTotalVideos()}</div>
                            <div class="text-sm opacity-75">فيديو</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold">${this.formatDuration(this.currentCourse.estimatedDuration)}</div>
                            <div class="text-sm opacity-75">المدة الإجمالية</div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // معلومات الدورة
    renderCourseInfo() {
        return `
            <div>
                <h3 class="text-xl font-bold text-gray-900 mb-4">حول هذه الدورة</h3>
                <p class="text-gray-700 mb-6">${this.currentCourse.description || 'لا يوجد وصف متاح'}</p>
                
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                    <div class="text-center p-3 bg-gray-50 rounded-lg">
                        <div class="text-lg font-bold text-primary-600">${this.currentCourse.sections?.length || 0}</div>
                        <div class="text-sm text-gray-600">أقسام</div>
                    </div>
                    <div class="text-center p-3 bg-gray-50 rounded-lg">
                        <div class="text-lg font-bold text-primary-600">${this.getTotalVideos()}</div>
                        <div class="text-sm text-gray-600">فيديوهات</div>
                    </div>
                    <div class="text-center p-3 bg-gray-50 rounded-lg">
                        <div class="text-lg font-bold text-primary-600">${this.formatDuration(this.currentCourse.estimatedDuration)}</div>
                        <div class="text-sm text-gray-600">المدة</div>
                    </div>
                    <div class="text-center p-3 bg-gray-50 rounded-lg">
                        <div class="text-lg font-bold text-primary-600">${this.getLevelText(this.currentCourse.difficultyLevel)}</div>
                        <div class="text-sm text-gray-600">المستوى</div>
                    </div>
                </div>

                <div class="border-t border-gray-200 pt-4">
                    <h4 class="font-medium text-gray-900 mb-2">المدرب</h4>
                    <div class="flex items-center">
                        <img class="h-10 w-10 rounded-full mr-3" 
                             src="${this.currentCourse.instructor?.avatarUrl || 'https://via.placeholder.com/40'}" 
                             alt="${this.currentCourse.instructor?.firstName}">
                        <div>
                            <p class="font-medium text-gray-900">${this.currentCourse.instructor?.firstName} ${this.currentCourse.instructor?.lastName}</p>
                            <p class="text-sm text-gray-600">${this.currentCourse.instructor?.bio || 'مدرب معتمد'}</p>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // معلومات الفيديو
    renderVideoInfo() {
        return `
            <div>
                <h3 class="text-xl font-bold text-gray-900 mb-2">${this.currentVideo.title}</h3>
                <p class="text-gray-700 mb-4">${this.currentVideo.description || 'لا يوجد وصف متاح'}</p>
                
                ${this.currentVideo.pdfs && this.currentVideo.pdfs.length > 0 ? `
                    <div class="border-t border-gray-200 pt-4">
                        <h4 class="font-medium text-gray-900 mb-3">المواد التعليمية</h4>
                        <div class="space-y-2">
                            ${this.currentVideo.pdfs.map(pdf => `
                                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                    <div class="flex items-center">
                                        <i data-feather="file-text" class="h-5 w-5 text-red-500 mr-3"></i>
                                        <div>
                                            <p class="font-medium text-gray-900">${pdf.title}</p>
                                            <p class="text-sm text-gray-600">${formatFileSize(pdf.fileSize)}</p>
                                        </div>
                                    </div>
                                    <div class="flex space-x-2 rtl:space-x-reverse">
                                        <button class="btn btn-sm btn-secondary" onclick="coursesManager.viewPdf('${pdf.pdfUrl}')">
                                            <i data-feather="eye" class="h-4 w-4"></i>
                                        </button>
                                        <button class="btn btn-sm btn-primary" onclick="coursesManager.downloadPdf('${pdf.pdfUrl}', '${pdf.title}')">
                                            <i data-feather="download" class="h-4 w-4"></i>
                                        </button>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                ` : ''}
            </div>
        `;
    }

    // تشغيل فيديو
    async playVideo(videoId) {
        try {
            showLoading();
            
            const response = await window.api.getVideo(videoId, { includeProgress: true });
            this.currentVideo = response.data.video;
            
            // تهيئة مشغل الفيديو
            this.initVideoPlayer();
            
            // تحديث معلومات الفيديو
            const infoContainer = document.getElementById('video-info-container');
            if (infoContainer) {
                infoContainer.innerHTML = this.renderVideoInfo();
                feather.replace();
            }
            
            // تحديث القائمة الجانبية
            this.updateSidebarSelection();
            
        } catch (error) {
            console.error('خطأ في تشغيل الفيديو:', error);
            showToast('فشل في تحميل الفيديو', 'error');
        } finally {
            hideLoading();
        }
    }

    // تهيئة مشغل الفيديو
    initVideoPlayer() {
        const container = document.getElementById('video-player-container');
        if (!container || !this.currentVideo) return;

        // تدمير المشغل السابق إن وجد
        if (this.videoPlayer) {
            this.videoPlayer.destroy();
        }

        // إنشاء مشغل جديد
        container.innerHTML = '<div id="video-player"></div>';
        this.videoPlayer = new VideoPlayer('video-player', {
            autoplay: false,
            controls: true,
            saveProgress: true
        });

        // تحميل الفيديو
        this.videoPlayer.loadVideo(this.currentVideo);
    }

    // تحديث تحديد القائمة الجانبية
    updateSidebarSelection() {
        // إزالة التحديد السابق
        document.querySelectorAll('.video-item').forEach(item => {
            item.classList.remove('bg-primary-50', 'border-primary-200');
        });

        // إضافة التحديد الجديد
        const currentItem = document.querySelector(`[onclick="coursesManager.playVideo('${this.currentVideo.id}')"]`);
        if (currentItem) {
            currentItem.classList.add('bg-primary-50', 'border-primary-200');
        }
    }

    // عرض PDF
    viewPdf(pdfUrl) {
        window.open(pdfUrl, '_blank');
    }

    // تحميل PDF
    downloadPdf(pdfUrl, fileName) {
        const link = document.createElement('a');
        link.href = pdfUrl;
        link.download = fileName;
        link.click();
    }

    // البحث في الدورات
    searchCourses(query) {
        // تطبيق البحث (يمكن تحسينه لاحقاً)
        console.log('البحث عن:', query);
    }

    // فلترة حسب الفئة
    filterByCategory(category) {
        console.log('فلترة حسب الفئة:', category);
    }

    // فلترة حسب المستوى
    filterByLevel(level) {
        console.log('فلترة حسب المستوى:', level);
    }

    // إظهار نافذة إنشاء دورة
    showCreateCourseModal() {
        showToast('ميزة إنشاء الدورات قريباً!', 'info');
    }

    // تعديل دورة
    editCourse(courseId) {
        showToast('ميزة تعديل الدورات قريباً!', 'info');
    }

    // حذف دورة
    async deleteCourse(courseId) {
        if (!confirm('هل أنت متأكد من حذف هذه الدورة؟')) return;
        
        try {
            showLoading();
            await window.api.deleteCourse(courseId);
            showToast('تم حذف الدورة بنجاح', 'success');
            this.loadCourses(); // إعادة تحميل القائمة
        } catch (error) {
            console.error('خطأ في حذف الدورة:', error);
            showToast('فشل في حذف الدورة', 'error');
        } finally {
            hideLoading();
        }
    }

    // التسجيل في دورة
    async enrollInCourse(courseId) {
        try {
            showLoading();
            await window.api.enrollInCourse(courseId, window.authManager.currentUser.id);
            showToast('تم التسجيل في الدورة بنجاح', 'success');
            this.loadCourses(); // إعادة تحميل القائمة
        } catch (error) {
            console.error('خطأ في التسجيل:', error);
            showToast('فشل في التسجيل في الدورة', 'error');
        } finally {
            hideLoading();
        }
    }

    // دوال مساعدة
    getLevelText(level) {
        const levels = {
            beginner: 'مبتدئ',
            intermediate: 'متوسط',
            advanced: 'متقدم'
        };
        return levels[level] || 'مبتدئ';
    }

    formatDuration(minutes) {
        if (!minutes) return '0 دقيقة';
        
        const hours = Math.floor(minutes / 60);
        const mins = minutes % 60;
        
        if (hours > 0) {
            return `${hours} ساعة ${mins > 0 ? `${mins} دقيقة` : ''}`;
        } else {
            return `${mins} دقيقة`;
        }
    }

    getTotalVideos() {
        if (!this.currentCourse.sections) return 0;
        return this.currentCourse.sections.reduce((total, section) => {
            return total + (section.videos?.length || 0);
        }, 0);
    }

    getLoadingHTML() {
        return `
            <div class="flex items-center justify-center h-64">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
            </div>
        `;
    }

    showError(message) {
        const container = document.getElementById('dashboard-container');
        container.innerHTML = `
            <div class="text-center py-12">
                <i data-feather="alert-circle" class="h-12 w-12 text-red-500 mx-auto mb-4"></i>
                <h2 class="text-xl font-bold text-gray-900 mb-2">خطأ</h2>
                <p class="text-gray-600">${message}</p>
                <button class="btn btn-primary mt-4" onclick="coursesManager.loadCourses()">المحاولة مرة أخرى</button>
            </div>
        `;
        feather.replace();
    }
}

// تهيئة مدير الدورات
window.coursesManager = new CoursesManager();
