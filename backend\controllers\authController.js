const { firebaseAuth } = require('../config/database');
const User = require('../models/User');
const { generateJWT } = require('../middleware/auth');
const { validationResult } = require('express-validator');

class AuthController {
  // Register new user
  static async register(req, res) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { 
        email, 
        password, 
        firstName, 
        lastName, 
        role = 'student',
        phone,
        bio 
      } = req.body;

      // Check if user already exists in our database
      const existingUser = await User.findByEmail(email);
      if (existingUser) {
        return res.status(400).json({
          success: false,
          message: 'User already exists with this email'
        });
      }

      // Create user in Firebase
      const firebaseUser = await firebaseAuth.createUser({
        email,
        password,
        displayName: `${firstName} ${lastName}`,
        emailVerified: false
      });

      // Set custom claims for role-based access
      await firebaseAuth.setCustomUserClaims(firebaseUser.uid, { role });

      // Create user in our database
      const user = await User.create({
        firebaseUid: firebaseUser.uid,
        email,
        firstName,
        lastName,
        role,
        phone,
        bio
      });

      // Generate JWT token
      const jwtToken = generateJWT(user);

      // Send email verification
      const verificationLink = await firebaseAuth.generateEmailVerificationLink(email);

      res.status(201).json({
        success: true,
        message: 'User registered successfully',
        data: {
          user: user.toJSON(),
          jwtToken,
          verificationLink: process.env.NODE_ENV === 'development' ? verificationLink : undefined
        }
      });

    } catch (error) {
      console.error('Registration error:', error);

      // Handle Firebase errors
      if (error.code === 'auth/email-already-exists') {
        return res.status(400).json({
          success: false,
          message: 'Email already exists in Firebase'
        });
      }

      if (error.code === 'auth/weak-password') {
        return res.status(400).json({
          success: false,
          message: 'Password is too weak'
        });
      }

      res.status(500).json({
        success: false,
        message: 'Registration failed',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  // Login user (verify Firebase token and return user data)
  static async login(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { idToken } = req.body;

      // Verify Firebase ID token
      const decodedToken = await firebaseAuth.verifyIdToken(idToken);
      
      // Get user from database
      const user = await User.findByFirebaseUid(decodedToken.uid);
      
      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found in database'
        });
      }

      if (!user.isActive) {
        return res.status(401).json({
          success: false,
          message: 'User account is deactivated'
        });
      }

      // Generate JWT token
      const jwtToken = generateJWT(user);

      // Update last login time (optional)
      // await user.update({ lastLoginAt: new Date() });

      res.json({
        success: true,
        message: 'Login successful',
        data: {
          user: user.toJSON(),
          jwtToken,
          firebaseToken: idToken
        }
      });

    } catch (error) {
      console.error('Login error:', error);

      if (error.code === 'auth/id-token-expired') {
        return res.status(401).json({
          success: false,
          message: 'Token expired',
          code: 'TOKEN_EXPIRED'
        });
      }

      if (error.code === 'auth/id-token-revoked') {
        return res.status(401).json({
          success: false,
          message: 'Token revoked',
          code: 'TOKEN_REVOKED'
        });
      }

      res.status(401).json({
        success: false,
        message: 'Invalid token',
        code: 'INVALID_TOKEN'
      });
    }
  }

  // Logout user (revoke Firebase token)
  static async logout(req, res) {
    try {
      const { firebaseUid } = req.firebaseUser;

      // Revoke all refresh tokens for the user
      await firebaseAuth.revokeRefreshTokens(firebaseUid);

      res.json({
        success: true,
        message: 'Logout successful'
      });

    } catch (error) {
      console.error('Logout error:', error);
      res.status(500).json({
        success: false,
        message: 'Logout failed'
      });
    }
  }

  // Get current user profile
  static async getProfile(req, res) {
    try {
      const user = req.user;

      res.json({
        success: true,
        data: {
          user: user.toJSON()
        }
      });

    } catch (error) {
      console.error('Get profile error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get user profile'
      });
    }
  }

  // Update user profile
  static async updateProfile(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const user = req.user;
      const { firstName, lastName, phone, bio, avatarUrl } = req.body;

      // Update user in database
      await user.update({
        firstName,
        lastName,
        phone,
        bio,
        avatarUrl
      });

      // Update Firebase display name if name changed
      if (firstName || lastName) {
        await firebaseAuth.updateUser(user.firebaseUid, {
          displayName: `${user.firstName} ${user.lastName}`
        });
      }

      res.json({
        success: true,
        message: 'Profile updated successfully',
        data: {
          user: user.toJSON()
        }
      });

    } catch (error) {
      console.error('Update profile error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update profile'
      });
    }
  }

  // Change password
  static async changePassword(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { newPassword } = req.body;
      const { firebaseUid } = req.firebaseUser;

      // Update password in Firebase
      await firebaseAuth.updateUser(firebaseUid, {
        password: newPassword
      });

      // Revoke all existing tokens to force re-login
      await firebaseAuth.revokeRefreshTokens(firebaseUid);

      res.json({
        success: true,
        message: 'Password changed successfully. Please login again.'
      });

    } catch (error) {
      console.error('Change password error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to change password'
      });
    }
  }

  // Send password reset email
  static async forgotPassword(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { email } = req.body;

      // Generate password reset link
      const resetLink = await firebaseAuth.generatePasswordResetLink(email);

      res.json({
        success: true,
        message: 'Password reset email sent successfully',
        resetLink: process.env.NODE_ENV === 'development' ? resetLink : undefined
      });

    } catch (error) {
      console.error('Forgot password error:', error);

      if (error.code === 'auth/user-not-found') {
        return res.status(404).json({
          success: false,
          message: 'No user found with this email'
        });
      }

      res.status(500).json({
        success: false,
        message: 'Failed to send password reset email'
      });
    }
  }

  // Verify email
  static async verifyEmail(req, res) {
    try {
      const { oobCode } = req.body;

      // Apply the email verification code
      await firebaseAuth.applyActionCode(oobCode);

      res.json({
        success: true,
        message: 'Email verified successfully'
      });

    } catch (error) {
      console.error('Email verification error:', error);
      res.status(400).json({
        success: false,
        message: 'Invalid or expired verification code'
      });
    }
  }

  // Refresh token
  static async refreshToken(req, res) {
    try {
      const user = req.user;
      const jwtToken = generateJWT(user);

      res.json({
        success: true,
        data: {
          jwtToken,
          user: user.toJSON()
        }
      });

    } catch (error) {
      console.error('Refresh token error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to refresh token'
      });
    }
  }
}

module.exports = AuthController;
