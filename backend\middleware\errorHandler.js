const errorHandler = (err, req, res, next) => {
  let error = { ...err };
  error.message = err.message;

  // Log error
  console.error('Error:', err);

  // Mongoose bad ObjectId
  if (err.name === 'CastError') {
    const message = 'Resource not found';
    error = { message, statusCode: 404 };
  }

  // Mongoose duplicate key
  if (err.code === 11000) {
    const message = 'Duplicate field value entered';
    error = { message, statusCode: 400 };
  }

  // Mongoose validation error
  if (err.name === 'ValidationError') {
    const message = Object.values(err.errors).map(val => val.message);
    error = { message, statusCode: 400 };
  }

  // JWT errors
  if (err.name === 'JsonWebTokenError') {
    const message = 'Invalid token';
    error = { message, statusCode: 401 };
  }

  if (err.name === 'TokenExpiredError') {
    const message = 'Token expired';
    error = { message, statusCode: 401 };
  }

  // Supabase errors
  if (err.code && err.code.startsWith('PGRST')) {
    let message = 'Database error';
    let statusCode = 500;

    switch (err.code) {
      case 'PGRST116':
        message = 'Resource not found';
        statusCode = 404;
        break;
      case 'PGRST301':
        message = 'Duplicate resource';
        statusCode = 409;
        break;
      default:
        message = err.message || 'Database error';
    }

    error = { message, statusCode };
  }

  // Firebase errors
  if (err.code && err.code.startsWith('auth/')) {
    let message = 'Authentication error';
    let statusCode = 401;

    switch (err.code) {
      case 'auth/user-not-found':
        message = 'User not found';
        statusCode = 404;
        break;
      case 'auth/wrong-password':
        message = 'Invalid credentials';
        statusCode = 401;
        break;
      case 'auth/email-already-in-use':
        message = 'Email already in use';
        statusCode = 400;
        break;
      case 'auth/weak-password':
        message = 'Password is too weak';
        statusCode = 400;
        break;
      case 'auth/id-token-expired':
        message = 'Token expired';
        statusCode = 401;
        break;
      case 'auth/id-token-revoked':
        message = 'Token revoked';
        statusCode = 401;
        break;
      default:
        message = err.message || 'Authentication error';
    }

    error = { message, statusCode };
  }

  // File upload errors
  if (err.code === 'LIMIT_FILE_SIZE') {
    const message = 'File too large';
    error = { message, statusCode: 400 };
  }

  if (err.code === 'LIMIT_FILE_COUNT') {
    const message = 'Too many files';
    error = { message, statusCode: 400 };
  }

  if (err.code === 'LIMIT_UNEXPECTED_FILE') {
    const message = 'Unexpected file field';
    error = { message, statusCode: 400 };
  }

  res.status(error.statusCode || 500).json({
    success: false,
    message: error.message || 'Server Error',
    ...(process.env.NODE_ENV === 'development' && {
      stack: err.stack,
      error: err
    })
  });
};

module.exports = errorHandler;
