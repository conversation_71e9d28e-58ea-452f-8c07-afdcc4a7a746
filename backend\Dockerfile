# استخدام Node.js الرسمي كصورة أساسية
FROM node:18-alpine

# تعيين مجلد العمل
WORKDIR /app

# نسخ ملفات package.json و package-lock.json
COPY package*.json ./

# تثبيت التبعيات
RUN npm ci --only=production

# نسخ باقي ملفات التطبيق
COPY . .

# إنشاء مستخدم غير جذر لتشغيل التطبيق
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nodejs -u 1001

# تغيير ملكية الملفات للمستخدم الجديد
RUN chown -R nodejs:nodejs /app
USER nodejs

# كشف المنفذ
EXPOSE 3000

# تعيين متغيرات البيئة
ENV NODE_ENV=production
ENV PORT=3000

# فحص صحة التطبيق
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node healthcheck.js

# تشغيل التطبيق
CMD ["npm", "start"]
