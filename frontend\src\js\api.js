// API module for handling HTTP requests
class ApiClient {
    constructor() {
        this.baseURL = apiConfig.baseURL;
        this.timeout = apiConfig.timeout;
        this.retries = apiConfig.retries;
    }

    async request(endpoint, options = {}) {
        const url = `${this.baseURL}${endpoint}`;
        const config = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        };

        // Add auth headers if user is authenticated
        if (window.authManager && window.authManager.isAuthenticated) {
            config.headers.Authorization = `Bearer ${window.authManager.authToken}`;
        }

        let lastError;
        for (let attempt = 0; attempt < this.retries; attempt++) {
            try {
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), this.timeout);

                const response = await fetch(url, {
                    ...config,
                    signal: controller.signal
                });

                clearTimeout(timeoutId);

                if (response.status === 401) {
                    // Unauthorized - redirect to login
                    if (window.authManager) {
                        window.authManager.logout();
                    }
                    throw new Error('Session expired. Please log in again.');
                }

                if (!response.ok) {
                    const errorData = await response.json().catch(() => ({}));
                    throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
                }

                return await response.json();
            } catch (error) {
                lastError = error;
                
                if (error.name === 'AbortError') {
                    throw new Error('Request timeout');
                }

                // Don't retry on client errors (4xx)
                if (error.message.includes('HTTP 4')) {
                    throw error;
                }

                // Wait before retrying
                if (attempt < this.retries - 1) {
                    await new Promise(resolve => setTimeout(resolve, 1000 * (attempt + 1)));
                }
            }
        }

        throw lastError;
    }

    // Auth endpoints
    async login(credentials) {
        return this.request('/auth/login', {
            method: 'POST',
            body: JSON.stringify(credentials)
        });
    }

    async register(userData) {
        return this.request('/auth/register', {
            method: 'POST',
            body: JSON.stringify(userData)
        });
    }

    async logout() {
        return this.request('/auth/logout', {
            method: 'POST'
        });
    }

    async getProfile() {
        return this.request('/auth/profile');
    }

    async updateProfile(profileData) {
        return this.request('/auth/profile', {
            method: 'PUT',
            body: JSON.stringify(profileData)
        });
    }

    async forgotPassword(email) {
        return this.request('/auth/forgot-password', {
            method: 'POST',
            body: JSON.stringify({ email })
        });
    }

    // Course endpoints
    async getCourses(params = {}) {
        const queryString = new URLSearchParams(params).toString();
        return this.request(`/courses${queryString ? `?${queryString}` : ''}`);
    }

    async getCourse(id) {
        return this.request(`/courses/${id}`);
    }

    async createCourse(courseData) {
        return this.request('/courses', {
            method: 'POST',
            body: JSON.stringify(courseData)
        });
    }

    async updateCourse(id, courseData) {
        return this.request(`/courses/${id}`, {
            method: 'PUT',
            body: JSON.stringify(courseData)
        });
    }

    async deleteCourse(id) {
        return this.request(`/courses/${id}`, {
            method: 'DELETE'
        });
    }

    async enrollInCourse(courseId, studentId) {
        return this.request(`/courses/${courseId}/enroll`, {
            method: 'POST',
            body: JSON.stringify({ studentId })
        });
    }

    async getCourseProgress(courseId) {
        return this.request(`/courses/${courseId}/progress`);
    }

    async updateCourseProgress(courseId, progressData) {
        return this.request(`/courses/${courseId}/progress`, {
            method: 'PUT',
            body: JSON.stringify(progressData)
        });
    }

    // Video endpoints
    async getVideos(sectionId, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        return this.request(`/videos/section/${sectionId}${queryString ? `?${queryString}` : ''}`);
    }

    async getVideo(id, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        return this.request(`/videos/${id}${queryString ? `?${queryString}` : ''}`);
    }

    async createVideo(videoData) {
        return this.request('/videos', {
            method: 'POST',
            body: JSON.stringify(videoData)
        });
    }

    async updateVideo(id, videoData) {
        return this.request(`/videos/${id}`, {
            method: 'PUT',
            body: JSON.stringify(videoData)
        });
    }

    async deleteVideo(id) {
        return this.request(`/videos/${id}`, {
            method: 'DELETE'
        });
    }

    async updateVideoProgress(videoId, progressData) {
        return this.request(`/videos/${videoId}/progress`, {
            method: 'POST',
            body: JSON.stringify(progressData)
        });
    }

    // Quiz endpoints
    async getQuizzes(courseId, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        return this.request(`/quizzes/course/${courseId}${queryString ? `?${queryString}` : ''}`);
    }

    async getQuiz(id, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        return this.request(`/quizzes/${id}${queryString ? `?${queryString}` : ''}`);
    }

    async createQuiz(quizData) {
        return this.request('/quizzes', {
            method: 'POST',
            body: JSON.stringify(quizData)
        });
    }

    async updateQuiz(id, quizData) {
        return this.request(`/quizzes/${id}`, {
            method: 'PUT',
            body: JSON.stringify(quizData)
        });
    }

    async deleteQuiz(id) {
        return this.request(`/quizzes/${id}`, {
            method: 'DELETE'
        });
    }

    async submitQuiz(quizId, answers, timeTaken) {
        return this.request(`/quizzes/${quizId}/submit`, {
            method: 'POST',
            body: JSON.stringify({ answers, timeTaken })
        });
    }

    async getQuizAttempts(quizId) {
        return this.request(`/quizzes/${quizId}/attempts`);
    }

    async getQuizStatistics(quizId) {
        return this.request(`/quizzes/${quizId}/statistics`);
    }

    // Upload endpoints
    async uploadVideo(file, onProgress) {
        return this.uploadFile('/upload/video', file, 'video', onProgress);
    }

    async uploadPdf(file, onProgress) {
        return this.uploadFile('/upload/pdf', file, 'pdf', onProgress);
    }

    async uploadImage(file, type = 'general', onProgress) {
        const formData = new FormData();
        formData.append('image', file);
        formData.append('type', type);
        
        return this.uploadFile('/upload/image', formData, null, onProgress);
    }

    async uploadFile(endpoint, file, fieldName, onProgress) {
        const url = `${this.baseURL}${endpoint}`;
        const formData = new FormData();
        
        if (fieldName) {
            formData.append(fieldName, file);
        } else if (file instanceof FormData) {
            // File is already FormData
            for (const [key, value] of file.entries()) {
                formData.append(key, value);
            }
        }

        return new Promise((resolve, reject) => {
            const xhr = new XMLHttpRequest();

            // Add auth header
            if (window.authManager && window.authManager.isAuthenticated) {
                xhr.setRequestHeader('Authorization', `Bearer ${window.authManager.authToken}`);
            }

            // Progress tracking
            if (onProgress) {
                xhr.upload.addEventListener('progress', (event) => {
                    if (event.lengthComputable) {
                        const percentComplete = (event.loaded / event.total) * 100;
                        onProgress(percentComplete);
                    }
                });
            }

            xhr.addEventListener('load', () => {
                if (xhr.status >= 200 && xhr.status < 300) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        resolve(response);
                    } catch (error) {
                        reject(new Error('Invalid response format'));
                    }
                } else {
                    try {
                        const errorResponse = JSON.parse(xhr.responseText);
                        reject(new Error(errorResponse.message || `Upload failed: ${xhr.status}`));
                    } catch (error) {
                        reject(new Error(`Upload failed: ${xhr.status}`));
                    }
                }
            });

            xhr.addEventListener('error', () => {
                reject(new Error('Upload failed: Network error'));
            });

            xhr.addEventListener('timeout', () => {
                reject(new Error('Upload failed: Timeout'));
            });

            xhr.timeout = this.timeout;
            xhr.open('POST', url);
            xhr.send(formData);
        });
    }

    async deleteFile(bucket, filePath) {
        return this.request('/upload/file', {
            method: 'DELETE',
            body: JSON.stringify({ bucket, filePath })
        });
    }

    // User endpoints
    async getUsers(params = {}) {
        const queryString = new URLSearchParams(params).toString();
        return this.request(`/users${queryString ? `?${queryString}` : ''}`);
    }

    async searchUsers(query, role = null, limit = 20) {
        const params = { q: query, limit };
        if (role) params.role = role;
        const queryString = new URLSearchParams(params).toString();
        return this.request(`/users/search?${queryString}`);
    }

    async getUser(id) {
        return this.request(`/users/${id}`);
    }

    async updateUser(id, userData) {
        return this.request(`/users/${id}`, {
            method: 'PUT',
            body: JSON.stringify(userData)
        });
    }

    async deleteUser(id) {
        return this.request(`/users/${id}`, {
            method: 'DELETE'
        });
    }

    async getUsersByRole(role, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        return this.request(`/users/role/${role}${queryString ? `?${queryString}` : ''}`);
    }
}

// Initialize API client
window.api = new ApiClient();
