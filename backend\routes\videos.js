const express = require('express');
const { body, param, query } = require('express-validator');
const Video = require('../models/Video');
const {
  verifyFirebaseToken,
  requireAuthenticated,
  requireInstructorOrAdmin,
  optionalAuth
} = require('../middleware/auth');

const router = express.Router();

// Validation rules
const createVideoValidation = [
  body('sectionId')
    .isUUID()
    .withMessage('Section ID must be a valid UUID'),
  body('title')
    .trim()
    .isLength({ min: 3, max: 255 })
    .withMessage('Title must be between 3 and 255 characters'),
  body('description')
    .optional()
    .isLength({ max: 2000 })
    .withMessage('Description must not exceed 2000 characters'),
  body('videoUrl')
    .isURL()
    .withMessage('Video URL must be a valid URL'),
  body('thumbnailUrl')
    .optional()
    .isURL()
    .withMessage('Thumbnail URL must be a valid URL'),
  body('duration')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Duration must be a positive integer (seconds)'),
  body('orderIndex')
    .isInt({ min: 0 })
    .withMessage('Order index must be a non-negative integer'),
  body('isPreview')
    .optional()
    .isBoolean()
    .withMessage('isPreview must be a boolean')
];

const updateVideoValidation = [
  body('title')
    .optional()
    .trim()
    .isLength({ min: 3, max: 255 })
    .withMessage('Title must be between 3 and 255 characters'),
  body('description')
    .optional()
    .isLength({ max: 2000 })
    .withMessage('Description must not exceed 2000 characters'),
  body('videoUrl')
    .optional()
    .isURL()
    .withMessage('Video URL must be a valid URL'),
  body('thumbnailUrl')
    .optional()
    .isURL()
    .withMessage('Thumbnail URL must be a valid URL'),
  body('duration')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Duration must be a positive integer (seconds)'),
  body('orderIndex')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Order index must be a non-negative integer'),
  body('isPreview')
    .optional()
    .isBoolean()
    .withMessage('isPreview must be a boolean')
];

const progressValidation = [
  body('watchedDuration')
    .isInt({ min: 0 })
    .withMessage('Watched duration must be a non-negative integer (seconds)'),
  body('isCompleted')
    .isBoolean()
    .withMessage('isCompleted must be a boolean')
];

const addPdfValidation = [
  body('title')
    .trim()
    .isLength({ min: 1, max: 255 })
    .withMessage('Title must be between 1 and 255 characters'),
  body('description')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Description must not exceed 500 characters'),
  body('pdfUrl')
    .isURL()
    .withMessage('PDF URL must be a valid URL'),
  body('fileSize')
    .optional()
    .isInt({ min: 1 })
    .withMessage('File size must be a positive integer')
];

const idValidation = [
  param('id')
    .isUUID()
    .withMessage('Video ID must be a valid UUID')
];

// Routes

/**
 * @route   GET /api/videos/section/:sectionId
 * @desc    Get all videos in a section
 * @access  Public (preview videos) / Private (all videos for enrolled students)
 */
router.get('/section/:sectionId',
  optionalAuth,
  [param('sectionId').isUUID().withMessage('Section ID must be a valid UUID')],
  async (req, res) => {
    try {
      const { sectionId } = req.params;
      const { includeProgress = false } = req.query;

      const options = {
        includeProgress: includeProgress === 'true' && req.user,
        studentId: req.user?.id
      };

      const videos = await Video.findAll(sectionId, options);

      // Filter preview videos for non-enrolled users
      let filteredVideos = videos;
      if (!req.user) {
        filteredVideos = videos.filter(video => video.isPreview);
      }

      res.json({
        success: true,
        data: {
          videos: filteredVideos.map(video => video.toJSON())
        }
      });

    } catch (error) {
      console.error('Get videos error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch videos'
      });
    }
  }
);

/**
 * @route   GET /api/videos/:id
 * @desc    Get video by ID
 * @access  Public (preview videos) / Private (enrolled students)
 */
router.get('/:id',
  optionalAuth,
  idValidation,
  async (req, res) => {
    try {
      const { id } = req.params;
      const { includeProgress = false } = req.query;

      const options = {
        includeProgress: includeProgress === 'true' && req.user,
        studentId: req.user?.id
      };

      const video = await Video.findById(id, options);

      if (!video) {
        return res.status(404).json({
          success: false,
          message: 'Video not found'
        });
      }

      // Check access permissions
      if (!video.isPreview && !req.user) {
        return res.status(401).json({
          success: false,
          message: 'Authentication required to access this video'
        });
      }

      res.json({
        success: true,
        data: {
          video: video.toJSON()
        }
      });

    } catch (error) {
      console.error('Get video error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch video'
      });
    }
  }
);

/**
 * @route   POST /api/videos
 * @desc    Create new video
 * @access  Private (Instructor/Admin)
 */
router.post('/',
  verifyFirebaseToken,
  requireInstructorOrAdmin,
  createVideoValidation,
  async (req, res) => {
    try {
      const video = await Video.create(req.body);

      res.status(201).json({
        success: true,
        message: 'Video created successfully',
        data: {
          video: video.toJSON()
        }
      });

    } catch (error) {
      console.error('Create video error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to create video'
      });
    }
  }
);

/**
 * @route   PUT /api/videos/:id
 * @desc    Update video
 * @access  Private (Course owner/Admin)
 */
router.put('/:id',
  verifyFirebaseToken,
  requireInstructorOrAdmin,
  idValidation,
  updateVideoValidation,
  async (req, res) => {
    try {
      const { id } = req.params;
      const video = await Video.findById(id);

      if (!video) {
        return res.status(404).json({
          success: false,
          message: 'Video not found'
        });
      }

      await video.update(req.body);

      res.json({
        success: true,
        message: 'Video updated successfully',
        data: {
          video: video.toJSON()
        }
      });

    } catch (error) {
      console.error('Update video error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update video'
      });
    }
  }
);

/**
 * @route   DELETE /api/videos/:id
 * @desc    Delete video
 * @access  Private (Course owner/Admin)
 */
router.delete('/:id',
  verifyFirebaseToken,
  requireInstructorOrAdmin,
  idValidation,
  async (req, res) => {
    try {
      const { id } = req.params;
      const video = await Video.findById(id);

      if (!video) {
        return res.status(404).json({
          success: false,
          message: 'Video not found'
        });
      }

      await video.delete();

      res.json({
        success: true,
        message: 'Video deleted successfully'
      });

    } catch (error) {
      console.error('Delete video error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to delete video'
      });
    }
  }
);

/**
 * @route   POST /api/videos/:id/progress
 * @desc    Update video progress
 * @access  Private (Student)
 */
router.post('/:id/progress',
  verifyFirebaseToken,
  requireAuthenticated,
  idValidation,
  progressValidation,
  async (req, res) => {
    try {
      const { id } = req.params;
      const studentId = req.user.id;

      const video = await Video.findById(id);

      if (!video) {
        return res.status(404).json({
          success: false,
          message: 'Video not found'
        });
      }

      const progress = await video.updateProgress(studentId, req.body);

      res.json({
        success: true,
        message: 'Progress updated successfully',
        data: {
          progress
        }
      });

    } catch (error) {
      console.error('Update progress error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update progress'
      });
    }
  }
);

/**
 * @route   POST /api/videos/:id/pdfs
 * @desc    Add PDF to video
 * @access  Private (Instructor/Admin)
 */
router.post('/:id/pdfs',
  verifyFirebaseToken,
  requireInstructorOrAdmin,
  idValidation,
  addPdfValidation,
  async (req, res) => {
    try {
      const { id } = req.params;
      const video = await Video.findById(id);

      if (!video) {
        return res.status(404).json({
          success: false,
          message: 'Video not found'
        });
      }

      const pdf = await video.addPdf(req.body);

      res.status(201).json({
        success: true,
        message: 'PDF added successfully',
        data: {
          pdf
        }
      });

    } catch (error) {
      console.error('Add PDF error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to add PDF'
      });
    }
  }
);

/**
 * @route   DELETE /api/videos/:id/pdfs/:pdfId
 * @desc    Remove PDF from video
 * @access  Private (Instructor/Admin)
 */
router.delete('/:id/pdfs/:pdfId',
  verifyFirebaseToken,
  requireInstructorOrAdmin,
  [
    param('id').isUUID().withMessage('Video ID must be a valid UUID'),
    param('pdfId').isUUID().withMessage('PDF ID must be a valid UUID')
  ],
  async (req, res) => {
    try {
      const { id, pdfId } = req.params;
      const video = await Video.findById(id);

      if (!video) {
        return res.status(404).json({
          success: false,
          message: 'Video not found'
        });
      }

      await video.removePdf(pdfId);

      res.json({
        success: true,
        message: 'PDF removed successfully'
      });

    } catch (error) {
      console.error('Remove PDF error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to remove PDF'
      });
    }
  }
);

module.exports = router;
