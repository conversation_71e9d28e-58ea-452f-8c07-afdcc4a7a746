const { createClient } = require('@supabase/supabase-js');
const admin = require('firebase-admin');
require('dotenv').config();

// Initialize Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

// Initialize Firebase Admin
let firebaseApp;
try {
  const serviceAccount = {
    type: "service_account",
    project_id: process.env.FIREBASE_PROJECT_ID,
    private_key_id: process.env.FIREBASE_PRIVATE_KEY_ID,
    private_key: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
    client_email: process.env.FIREBASE_CLIENT_EMAIL,
    client_id: process.env.FIREBASE_CLIENT_ID,
    auth_uri: process.env.FIREBASE_AUTH_URI,
    token_uri: process.env.FIREBASE_TOKEN_URI,
    auth_provider_x509_cert_url: process.env.FIREBASE_AUTH_PROVIDER_X509_CERT_URL,
    client_x509_cert_url: process.env.FIREBASE_CLIENT_X509_CERT_URL
  };

  firebaseApp = admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    projectId: process.env.FIREBASE_PROJECT_ID
  });

  console.log('✅ Firebase Admin initialized successfully');
} catch (error) {
  console.error('❌ Firebase Admin initialization failed:', error.message);
}

// Database connection test
async function testConnections() {
  try {
    // Test Supabase connection
    const { data, error } = await supabase
      .from('users')
      .select('count')
      .limit(1);

    if (error) {
      console.error('❌ Supabase connection failed:', error.message);
    } else {
      console.log('✅ Supabase connection successful');
    }

    // Test Firebase connection
    if (firebaseApp) {
      await admin.auth().listUsers(1);
      console.log('✅ Firebase connection successful');
    }
  } catch (error) {
    console.error('❌ Database connection test failed:', error.message);
  }
}

// Helper functions for database operations
const dbHelpers = {
  // Execute raw SQL query
  async executeQuery(query, params = []) {
    const { data, error } = await supabase.rpc('execute_sql', {
      query,
      params
    });

    if (error) throw error;
    return data;
  },

  // Get table info
  async getTableInfo(tableName) {
    const { data, error } = await supabase
      .from('information_schema.columns')
      .select('column_name, data_type, is_nullable')
      .eq('table_name', tableName);

    if (error) throw error;
    return data;
  },

  // Check if table exists
  async tableExists(tableName) {
    const { data, error } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_name', tableName)
      .eq('table_schema', 'public');

    if (error) throw error;
    return data.length > 0;
  },

  // Create backup
  async createBackup(tables = []) {
    const backup = {};
    
    for (const table of tables) {
      const { data, error } = await supabase
        .from(table)
        .select('*');

      if (error) {
        console.error(`Error backing up table ${table}:`, error);
        continue;
      }

      backup[table] = data;
    }

    return backup;
  },

  // Restore from backup
  async restoreFromBackup(backup) {
    for (const [table, data] of Object.entries(backup)) {
      if (data.length === 0) continue;

      const { error } = await supabase
        .from(table)
        .insert(data);

      if (error) {
        console.error(`Error restoring table ${table}:`, error);
      }
    }
  }
};

// Storage helpers
const storageHelpers = {
  // Upload file to Supabase Storage
  async uploadFile(bucket, path, file, options = {}) {
    const { data, error } = await supabase.storage
      .from(bucket)
      .upload(path, file, {
        cacheControl: '3600',
        upsert: false,
        ...options
      });

    if (error) throw error;
    return data;
  },

  // Get file URL
  async getFileUrl(bucket, path) {
    const { data } = supabase.storage
      .from(bucket)
      .getPublicUrl(path);

    return data.publicUrl;
  },

  // Delete file
  async deleteFile(bucket, path) {
    const { error } = await supabase.storage
      .from(bucket)
      .remove([path]);

    if (error) throw error;
    return true;
  },

  // List files in bucket
  async listFiles(bucket, folder = '') {
    const { data, error } = await supabase.storage
      .from(bucket)
      .list(folder);

    if (error) throw error;
    return data;
  }
};

module.exports = {
  supabase,
  firebaseApp,
  firebaseAuth: firebaseApp ? admin.auth() : null,
  firestore: firebaseApp ? admin.firestore() : null,
  testConnections,
  dbHelpers,
  storageHelpers
};
