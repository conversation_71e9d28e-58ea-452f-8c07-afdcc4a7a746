# دليل إعداد وتشغيل منصة الدورات التدريبية

## 📋 نظرة عامة

تم إنشاء منصة شاملة للدورات التدريبية الإلكترونية تتضمن:

### ✅ المكونات المكتملة

#### الخادم الخلفي (Backend)
- **Express.js Server** مع جميع المسارات والتحكمات
- **نظام المصادقة** باستخدام Firebase Authentication
- **إدارة الملفات** مع دعم رفع الفيديوهات والـ PDF
- **نماذج البيانات** للمستخدمين والدورات والفيديوهات والاختبارات
- **API شامل** لجميع العمليات المطلوبة
- **وسطاء الأمان** والتحقق من الصحة

#### الواجهة الأمامية (Frontend)
- **واجهة مستخدم متجاوبة** باستخدام HTML5 و CSS3 و JavaScript
- **نظام إدارة الحالة** للمصادقة والبيانات
- **مشغل فيديو متقدم** مع حفظ التقدم
- **نظام الاختبارات التفاعلي** مع أنواع أسئلة متعددة
- **لوحة تحكم** مختلفة لكل نوع مستخدم
- **نظام رفع الملفات** مع شريط التقدم

#### قاعدة البيانات
- **مخطط قاعدة بيانات PostgreSQL** كامل
- **قواعد الأمان (RLS)** لحماية البيانات
- **فهارس محسنة** لتحسين الأداء
- **دوال مخصصة** لحساب التقدم والبحث

#### DevOps والنشر
- **Docker Compose** للتطوير المحلي
- **Dockerfiles** للخادم والواجهة
- **إعدادات Nginx** للإنتاج
- **متغيرات البيئة** المنظمة

## 🚀 خطوات التشغيل السريع

### 1. متطلبات النظام
```bash
# تأكد من وجود هذه الأدوات
node --version  # v18.0.0+
npm --version   # v8.0.0+
docker --version
docker-compose --version
```

### 2. استنساخ المشروع
```bash
git clone <repository-url>
cd online-courses-platform
```

### 3. إعداد متغيرات البيئة
```bash
# نسخ ملف المتغيرات
cp backend/.env.example backend/.env

# تعديل المتغيرات (مطلوب)
nano backend/.env
```

**المتغيرات المطلوبة:**
```env
# Firebase
FIREBASE_PROJECT_ID=your-project-id
FIREBASE_PRIVATE_KEY="your-private-key"
FIREBASE_CLIENT_EMAIL=<EMAIL>

# Supabase
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
SUPABASE_ANON_KEY=your-anon-key

# JWT
JWT_SECRET=your-super-secret-key
```

### 4. إعداد Firebase

#### إنشاء مشروع Firebase
1. اذهب إلى [Firebase Console](https://console.firebase.google.com/)
2. أنشئ مشروع جديد
3. فعّل Authentication > Email/Password
4. أنشئ Service Account وحمّل المفتاح

#### إعداد Firestore (اختياري)
```javascript
// في Firebase Console > Firestore
// ارفع قواعد الأمان:
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /{document=**} {
      allow read, write: if request.auth != null;
    }
  }
}
```

### 5. إعداد Supabase

#### إنشاء مشروع Supabase
1. اذهب إلى [Supabase](https://supabase.com/)
2. أنشئ مشروع جديد
3. احصل على URL و API Keys
4. شغّل سكريبت قاعدة البيانات:

```bash
# في Supabase SQL Editor
# ارفع محتوى ملف database/schema.sql
# ثم ارفع محتوى ملف supabase/policies.sql
```

### 6. تشغيل المشروع

#### الطريقة الأولى: Docker (مُوصى بها)
```bash
# تشغيل جميع الخدمات
docker-compose up -d

# مراقبة السجلات
docker-compose logs -f backend
```

#### الطريقة الثانية: تشغيل محلي
```bash
# تثبيت التبعيات
cd backend
npm install

# تشغيل الخادم الخلفي
npm run dev

# في نافذة طرفية جديدة
cd ../frontend
python -m http.server 3001
# أو استخدم أي خادم ويب محلي آخر
```

### 7. الوصول للتطبيق
- **الواجهة الأمامية**: http://localhost:3001
- **API الخادم الخلفي**: http://localhost:3000
- **Supabase Dashboard**: في لوحة تحكم Supabase

## 🔧 إعدادات إضافية

### إعداد التخزين السحابي
```bash
# في Supabase Dashboard > Storage
# أنشئ Buckets التالية:
- avatars (public)
- videos (private)
- pdfs (private)
- certificates (private)
```

### إعداد البريد الإلكتروني (اختياري)
```env
# في .env
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
```

## 👥 إنشاء المستخدمين الأوليين

### 1. إنشاء حساب إداري
```bash
# سجّل دخول في التطبيق كمستخدم جديد
# ثم في Supabase SQL Editor:
UPDATE users 
SET role = 'admin' 
WHERE email = '<EMAIL>';
```

### 2. إنشاء حساب مدرب
```bash
# سجّل دخول كمستخدم جديد
# ثم في Supabase SQL Editor:
UPDATE users 
SET role = 'instructor' 
WHERE email = '<EMAIL>';
```

## 🧪 اختبار النظام

### 1. اختبار المصادقة
- سجّل حساب جديد
- سجّل دخول/خروج
- تحقق من تحديث الملف الشخصي

### 2. اختبار الدورات (كمدرب)
- أنشئ دورة جديدة
- أضف أقسام وفيديوهات
- ارفع ملفات PDF
- أنشئ اختبار

### 3. اختبار التعلم (كطالب)
- تصفح الدورات
- سجّل في دورة
- شاهد الفيديوهات
- أجرِ اختبار

## 🔍 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### خطأ في الاتصال بقاعدة البيانات
```bash
# تحقق من متغيرات Supabase
echo $SUPABASE_URL
echo $SUPABASE_SERVICE_ROLE_KEY

# تحقق من الاتصال
curl -H "apikey: $SUPABASE_ANON_KEY" $SUPABASE_URL/rest/v1/
```

#### خطأ في Firebase Authentication
```bash
# تحقق من إعدادات Firebase
# تأكد من تفعيل Email/Password في Console
# تحقق من صحة Service Account Key
```

#### مشاكل رفع الملفات
```bash
# تحقق من إعدادات Storage في Supabase
# تأكد من وجود Buckets المطلوبة
# تحقق من أحجام الملفات المسموحة
```

### سجلات النظام
```bash
# سجلات Docker
docker-compose logs backend
docker-compose logs frontend

# سجلات Node.js
tail -f backend/logs/app.log
```

## 📊 مراقبة الأداء

### مقاييس مهمة
- **استجابة API**: متوسط وقت الاستجابة
- **استخدام قاعدة البيانات**: عدد الاستعلامات والاتصالات
- **تخزين الملفات**: مساحة مستخدمة ومعدل النقل
- **المستخدمين النشطين**: عدد الجلسات المتزامنة

### أدوات المراقبة
- **Supabase Dashboard**: إحصائيات قاعدة البيانات
- **Firebase Console**: إحصائيات المصادقة
- **Server Logs**: سجلات الخادم والأخطاء

## 🚀 النشر للإنتاج

### 1. إعداد خادم الإنتاج
```bash
# تحديث متغيرات البيئة للإنتاج
NODE_ENV=production
DEBUG=false

# استخدام قاعدة بيانات إنتاج منفصلة
# تفعيل HTTPS
# إعداد النطاق المخصص
```

### 2. تحسينات الأمان
```bash
# تحديث كلمات المرور
# تفعيل Rate Limiting
# إعداد CORS للنطاقات المصرح بها
# تفعيل Helmet.js للأمان
```

### 3. النسخ الاحتياطي
```bash
# إعداد نسخ احتياطية تلقائية لقاعدة البيانات
# نسخ احتياطية للملفات المرفوعة
# مراقبة النظام والتنبيهات
```

## 📞 الدعم والمساعدة

### الموارد المفيدة
- [وثائق Express.js](https://expressjs.com/)
- [وثائق Firebase](https://firebase.google.com/docs)
- [وثائق Supabase](https://supabase.com/docs)
- [وثائق Docker](https://docs.docker.com/)

### التواصل
- **GitHub Issues**: للمشاكل التقنية
- **البريد الإلكتروني**: <EMAIL>

---

**ملاحظة**: هذا المشروع جاهز للاستخدام في بيئة التطوير. للإنتاج، يُنصح بإجراء اختبارات شاملة وتطبيق أفضل ممارسات الأمان.
