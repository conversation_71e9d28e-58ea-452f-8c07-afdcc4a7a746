// Authentication module
class AuthManager {
    constructor() {
        this.currentUser = null;
        this.authToken = null;
        this.isAuthenticated = false;
        
        // Initialize authentication state
        this.init();
    }

    async init() {
        try {
            // Check for stored authentication data
            const storedToken = localStorage.getItem(appConfig.storageKeys.authToken);
            const storedUser = localStorage.getItem(appConfig.storageKeys.user);

            if (storedToken && storedUser) {
                this.authToken = storedToken;
                this.currentUser = JSON.parse(storedUser);
                this.isAuthenticated = true;

                // Verify token is still valid
                const isValid = await this.verifyToken();
                if (!isValid) {
                    this.logout();
                    return;
                }

                this.showDashboard();
            } else {
                this.showAuth();
            }

            // Set up Firebase auth state listener
            if (window.auth) {
                window.auth.onAuthStateChanged((user) => {
                    if (user && !this.isAuthenticated) {
                        // User signed in through Firebase
                        this.handleFirebaseAuth(user);
                    } else if (!user && this.isAuthenticated) {
                        // User signed out
                        this.logout();
                    }
                });
            }
        } catch (error) {
            console.error('Auth initialization error:', error);
            this.showAuth();
        }
    }

    async handleFirebaseAuth(firebaseUser) {
        try {
            showLoading();
            
            // Get Firebase ID token
            const idToken = await firebaseUser.getIdToken();
            
            // Send to backend for verification and user data
            const response = await fetch(`${apiConfig.baseURL}/auth/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ idToken })
            });

            const data = await response.json();

            if (data.success) {
                this.setAuthData(data.data.user, data.data.jwtToken);
                this.showDashboard();
                showToast('Welcome back!', 'success');
            } else {
                throw new Error(data.message);
            }
        } catch (error) {
            console.error('Firebase auth error:', error);
            showToast(error.message || 'Authentication failed', 'error');
            this.logout();
        } finally {
            hideLoading();
        }
    }

    async login(email, password) {
        try {
            showLoading();

            // Sign in with Firebase
            const userCredential = await window.auth.signInWithEmailAndPassword(email, password);
            const idToken = await userCredential.user.getIdToken();

            // Send to backend
            const response = await fetch(`${apiConfig.baseURL}/auth/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ idToken })
            });

            const data = await response.json();

            if (data.success) {
                this.setAuthData(data.data.user, data.data.jwtToken);
                this.showDashboard();
                showToast(appConfig.successMessages.login, 'success');
                return true;
            } else {
                throw new Error(data.message);
            }
        } catch (error) {
            console.error('Login error:', error);
            let errorMessage = 'Login failed';
            
            if (error.code === 'auth/user-not-found') {
                errorMessage = 'No account found with this email';
            } else if (error.code === 'auth/wrong-password') {
                errorMessage = 'Incorrect password';
            } else if (error.code === 'auth/invalid-email') {
                errorMessage = 'Invalid email address';
            } else if (error.message) {
                errorMessage = error.message;
            }
            
            showToast(errorMessage, 'error');
            return false;
        } finally {
            hideLoading();
        }
    }

    async register(userData) {
        try {
            showLoading();

            // Create user with Firebase
            const userCredential = await window.auth.createUserWithEmailAndPassword(
                userData.email, 
                userData.password
            );

            // Update display name
            await userCredential.user.updateProfile({
                displayName: `${userData.firstName} ${userData.lastName}`
            });

            // Send to backend for user creation
            const response = await fetch(`${apiConfig.baseURL}/auth/register`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(userData)
            });

            const data = await response.json();

            if (data.success) {
                this.setAuthData(data.data.user, data.data.jwtToken);
                this.showDashboard();
                showToast(appConfig.successMessages.register, 'success');
                return true;
            } else {
                // Delete Firebase user if backend registration failed
                await userCredential.user.delete();
                throw new Error(data.message);
            }
        } catch (error) {
            console.error('Registration error:', error);
            let errorMessage = 'Registration failed';
            
            if (error.code === 'auth/email-already-in-use') {
                errorMessage = 'An account with this email already exists';
            } else if (error.code === 'auth/weak-password') {
                errorMessage = 'Password is too weak';
            } else if (error.code === 'auth/invalid-email') {
                errorMessage = 'Invalid email address';
            } else if (error.message) {
                errorMessage = error.message;
            }
            
            showToast(errorMessage, 'error');
            return false;
        } finally {
            hideLoading();
        }
    }

    async logout() {
        try {
            showLoading();

            // Sign out from Firebase
            if (window.auth.currentUser) {
                await window.auth.signOut();
            }

            // Call backend logout
            if (this.authToken) {
                await fetch(`${apiConfig.baseURL}/auth/logout`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${this.authToken}`
                    }
                });
            }

            this.clearAuthData();
            this.showAuth();
            showToast(appConfig.successMessages.logout, 'success');
        } catch (error) {
            console.error('Logout error:', error);
            // Clear data anyway
            this.clearAuthData();
            this.showAuth();
        } finally {
            hideLoading();
        }
    }

    async forgotPassword(email) {
        try {
            showLoading();

            await window.auth.sendPasswordResetEmail(email);
            showToast('Password reset email sent. Check your inbox.', 'success');
            return true;
        } catch (error) {
            console.error('Forgot password error:', error);
            let errorMessage = 'Failed to send password reset email';
            
            if (error.code === 'auth/user-not-found') {
                errorMessage = 'No account found with this email';
            } else if (error.code === 'auth/invalid-email') {
                errorMessage = 'Invalid email address';
            }
            
            showToast(errorMessage, 'error');
            return false;
        } finally {
            hideLoading();
        }
    }

    async updateProfile(profileData) {
        try {
            showLoading();

            const response = await fetch(`${apiConfig.baseURL}/auth/profile`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.authToken}`
                },
                body: JSON.stringify(profileData)
            });

            const data = await response.json();

            if (data.success) {
                this.currentUser = data.data.user;
                localStorage.setItem(appConfig.storageKeys.user, JSON.stringify(this.currentUser));
                showToast(appConfig.successMessages.profileUpdate, 'success');
                return true;
            } else {
                throw new Error(data.message);
            }
        } catch (error) {
            console.error('Profile update error:', error);
            showToast(error.message || 'Failed to update profile', 'error');
            return false;
        } finally {
            hideLoading();
        }
    }

    async verifyToken() {
        try {
            const response = await fetch(`${apiConfig.baseURL}/auth/profile`, {
                headers: {
                    'Authorization': `Bearer ${this.authToken}`
                }
            });

            return response.ok;
        } catch (error) {
            console.error('Token verification error:', error);
            return false;
        }
    }

    setAuthData(user, token) {
        this.currentUser = user;
        this.authToken = token;
        this.isAuthenticated = true;

        // Store in localStorage
        localStorage.setItem(appConfig.storageKeys.user, JSON.stringify(user));
        localStorage.setItem(appConfig.storageKeys.authToken, token);
    }

    clearAuthData() {
        this.currentUser = null;
        this.authToken = null;
        this.isAuthenticated = false;

        // Clear localStorage
        localStorage.removeItem(appConfig.storageKeys.user);
        localStorage.removeItem(appConfig.storageKeys.authToken);
    }

    showAuth() {
        document.getElementById('auth-container').classList.remove('hidden');
        document.getElementById('dashboard-container').classList.add('hidden');
        document.getElementById('navbar').classList.add('hidden');
    }

    showDashboard() {
        document.getElementById('auth-container').classList.add('hidden');
        document.getElementById('dashboard-container').classList.remove('hidden');
        document.getElementById('navbar').classList.remove('hidden');

        // Update navbar with user info
        this.updateNavbar();

        // Load dashboard content
        if (window.dashboardManager) {
            window.dashboardManager.loadDashboard();
        }
    }

    updateNavbar() {
        if (this.currentUser) {
            const userNameElement = document.getElementById('user-name');
            const userAvatarElement = document.getElementById('user-avatar');

            if (userNameElement) {
                userNameElement.textContent = this.currentUser.firstName || 'User';
            }

            if (userAvatarElement && this.currentUser.avatarUrl) {
                userAvatarElement.src = this.currentUser.avatarUrl;
            }

            // Show/hide navigation items based on role
            const studentsNav = document.getElementById('nav-students');
            if (studentsNav) {
                if (this.currentUser.role === 'instructor' || this.currentUser.role === 'admin') {
                    studentsNav.classList.remove('hidden');
                } else {
                    studentsNav.classList.add('hidden');
                }
            }
        }
    }

    getAuthHeaders() {
        return {
            'Authorization': `Bearer ${this.authToken}`,
            'Content-Type': 'application/json'
        };
    }

    hasRole(role) {
        return this.currentUser && this.currentUser.role === role;
    }

    hasAnyRole(roles) {
        return this.currentUser && roles.includes(this.currentUser.role);
    }
}

// Initialize auth manager
window.authManager = new AuthManager();
