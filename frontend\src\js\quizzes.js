// وحدة إدارة الاختبارات والكويزات
class QuizzesManager {
    constructor() {
        this.currentQuiz = null;
        this.currentAttempt = null;
        this.answers = [];
        this.timeRemaining = 0;
        this.timer = null;
        this.quizzes = [];
        this.userRole = null;
    }

    // تحميل صفحة الاختبارات
    async loadQuizzes() {
        try {
            this.userRole = window.authManager.currentUser?.role;
            const container = document.getElementById('dashboard-container');
            
            container.innerHTML = this.getLoadingHTML();

            // جلب الاختبارات (يمكن تحسينه لجلب اختبارات دورة محددة)
            // هنا نحتاج لتمرير معرف الدورة
            const courseId = this.getCurrentCourseId();
            if (courseId) {
                const response = await window.api.getQuizzes(courseId, { includeAttempts: true });
                this.quizzes = response.data.quizzes;
            } else {
                this.quizzes = [];
            }

            this.renderQuizzesPage();

        } catch (error) {
            console.error('خطأ في تحميل الاختبارات:', error);
            this.showError('فشل في تحميل الاختبارات');
        }
    }

    // عرض صفحة الاختبارات
    renderQuizzesPage() {
        const container = document.getElementById('dashboard-container');
        
        const html = `
            <div class="p-6">
                <div class="flex justify-between items-center mb-8">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">الاختبارات والكويزات</h1>
                        <p class="text-gray-600">اختبر معرفتك وتقدم في التعلم</p>
                    </div>
                    ${this.userRole === 'instructor' || this.userRole === 'admin' ? `
                        <button class="btn btn-primary" onclick="quizzesManager.showCreateQuizModal()">
                            <i data-feather="plus" class="h-4 w-4 mr-2"></i>
                            إنشاء اختبار جديد
                        </button>
                    ` : ''}
                </div>

                <!-- قائمة الاختبارات -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    ${this.renderQuizzesGrid()}
                </div>

                ${this.quizzes.length === 0 ? this.renderEmptyState() : ''}
            </div>
        `;

        container.innerHTML = html;
        feather.replace();
    }

    // عرض شبكة الاختبارات
    renderQuizzesGrid() {
        return this.quizzes.map(quiz => `
            <div class="card hover:shadow-lg transition-shadow duration-200">
                <div class="card-body">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-bold text-gray-900">${quiz.title}</h3>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${quiz.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}">
                            ${quiz.isActive ? 'نشط' : 'غير نشط'}
                        </span>
                    </div>
                    
                    <p class="text-gray-600 text-sm mb-4">${quiz.description || 'لا يوجد وصف متاح'}</p>
                    
                    <div class="space-y-2 mb-4">
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-gray-500">عدد الأسئلة:</span>
                            <span class="font-medium">${quiz.questions?.length || 0}</span>
                        </div>
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-gray-500">درجة النجاح:</span>
                            <span class="font-medium">${quiz.passingScore}%</span>
                        </div>
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-gray-500">الوقت المحدد:</span>
                            <span class="font-medium">${quiz.timeLimit ? `${quiz.timeLimit} دقيقة` : 'غير محدد'}</span>
                        </div>
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-gray-500">المحاولات المسموحة:</span>
                            <span class="font-medium">${quiz.maxAttempts}</span>
                        </div>
                    </div>

                    ${this.userRole === 'student' ? this.renderStudentQuizActions(quiz) : ''}
                    ${this.userRole === 'instructor' || this.userRole === 'admin' ? this.renderInstructorQuizActions(quiz) : ''}
                </div>
            </div>
        `).join('');
    }

    // أزرار الطالب للاختبار
    renderStudentQuizActions(quiz) {
        const attempts = quiz.studentAttempts || [];
        const canAttempt = attempts.length < quiz.maxAttempts;
        const bestScore = attempts.length > 0 ? Math.max(...attempts.map(a => a.score)) : 0;
        const passed = attempts.some(a => a.passed);

        return `
            <div class="border-t border-gray-200 pt-4">
                ${attempts.length > 0 ? `
                    <div class="mb-3">
                        <div class="flex items-center justify-between text-sm mb-1">
                            <span class="text-gray-500">أفضل نتيجة:</span>
                            <span class="font-medium ${passed ? 'text-green-600' : 'text-red-600'}">${bestScore}%</span>
                        </div>
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-gray-500">المحاولات:</span>
                            <span class="font-medium">${attempts.length}/${quiz.maxAttempts}</span>
                        </div>
                    </div>
                ` : ''}
                
                <div class="flex gap-2">
                    ${canAttempt ? `
                        <button class="btn btn-primary flex-1" onclick="quizzesManager.startQuiz('${quiz.id}')">
                            ${attempts.length > 0 ? 'إعادة المحاولة' : 'بدء الاختبار'}
                        </button>
                    ` : `
                        <div class="text-center text-sm text-gray-500 py-2">
                            تم استنفاد جميع المحاولات
                        </div>
                    `}
                    
                    ${attempts.length > 0 ? `
                        <button class="btn btn-secondary" onclick="quizzesManager.viewAttempts('${quiz.id}')">
                            <i data-feather="eye" class="h-4 w-4"></i>
                        </button>
                    ` : ''}
                </div>
            </div>
        `;
    }

    // أزرار المدرس للاختبار
    renderInstructorQuizActions(quiz) {
        return `
            <div class="border-t border-gray-200 pt-4">
                <div class="flex gap-2">
                    <button class="btn btn-primary btn-sm" onclick="quizzesManager.editQuiz('${quiz.id}')">
                        <i data-feather="edit" class="h-4 w-4 mr-1"></i>
                        تعديل
                    </button>
                    <button class="btn btn-secondary btn-sm" onclick="quizzesManager.viewStatistics('${quiz.id}')">
                        <i data-feather="bar-chart-2" class="h-4 w-4 mr-1"></i>
                        الإحصائيات
                    </button>
                    <button class="btn btn-danger btn-sm" onclick="quizzesManager.deleteQuiz('${quiz.id}')">
                        <i data-feather="trash-2" class="h-4 w-4"></i>
                    </button>
                </div>
            </div>
        `;
    }

    // بدء اختبار
    async startQuiz(quizId) {
        try {
            showLoading();
            
            const response = await window.api.getQuiz(quizId);
            this.currentQuiz = response.data.quiz;
            this.answers = new Array(this.currentQuiz.questions.length).fill(null);
            
            this.renderQuizInterface();
            
            // بدء المؤقت إذا كان هناك وقت محدد
            if (this.currentQuiz.timeLimit) {
                this.startTimer(this.currentQuiz.timeLimit * 60); // تحويل إلى ثوان
            }
            
        } catch (error) {
            console.error('خطأ في بدء الاختبار:', error);
            showToast('فشل في تحميل الاختبار', 'error');
        } finally {
            hideLoading();
        }
    }

    // عرض واجهة الاختبار
    renderQuizInterface() {
        const container = document.getElementById('dashboard-container');
        
        const html = `
            <div class="min-h-screen bg-gray-50">
                <!-- رأس الاختبار -->
                <div class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-10">
                    <div class="max-w-4xl mx-auto px-4 py-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <h1 class="text-xl font-bold text-gray-900">${this.currentQuiz.title}</h1>
                                <p class="text-sm text-gray-600">${this.currentQuiz.questions.length} سؤال • درجة النجاح: ${this.currentQuiz.passingScore}%</p>
                            </div>
                            <div class="flex items-center space-x-4 rtl:space-x-reverse">
                                ${this.currentQuiz.timeLimit ? `
                                    <div class="text-center">
                                        <div id="timer" class="text-2xl font-bold text-red-600">--:--</div>
                                        <div class="text-xs text-gray-500">الوقت المتبقي</div>
                                    </div>
                                ` : ''}
                                <button class="btn btn-danger" onclick="quizzesManager.exitQuiz()">
                                    <i data-feather="x" class="h-4 w-4 mr-2"></i>
                                    إنهاء الاختبار
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- محتوى الاختبار -->
                <div class="max-w-4xl mx-auto px-4 py-8">
                    <div class="space-y-8">
                        ${this.renderQuizQuestions()}
                    </div>
                    
                    <!-- أزرار التنقل -->
                    <div class="mt-8 flex justify-between items-center">
                        <div class="text-sm text-gray-500">
                            تم الإجابة على ${this.getAnsweredCount()} من ${this.currentQuiz.questions.length} أسئلة
                        </div>
                        <button class="btn btn-primary" onclick="quizzesManager.submitQuiz()">
                            <i data-feather="check" class="h-4 w-4 mr-2"></i>
                            تسليم الاختبار
                        </button>
                    </div>
                </div>
            </div>
        `;

        container.innerHTML = html;
        feather.replace();
    }

    // عرض أسئلة الاختبار
    renderQuizQuestions() {
        return this.currentQuiz.questions.map((question, index) => `
            <div class="quiz-question" id="question-${index}">
                <div class="flex items-start space-x-3 rtl:space-x-reverse mb-4">
                    <div class="flex-shrink-0 w-8 h-8 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center font-medium">
                        ${index + 1}
                    </div>
                    <div class="flex-1">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">${question.question}</h3>
                        ${this.renderQuestionOptions(question, index)}
                    </div>
                </div>
            </div>
        `).join('');
    }

    // عرض خيارات السؤال
    renderQuestionOptions(question, questionIndex) {
        switch (question.type) {
            case 'multiple_choice':
                return `
                    <div class="space-y-3">
                        ${question.options.map((option, optionIndex) => `
                            <label class="quiz-option ${this.answers[questionIndex] === optionIndex ? 'selected' : ''}" 
                                   onclick="quizzesManager.selectAnswer(${questionIndex}, ${optionIndex})">
                                <input type="radio" name="question-${questionIndex}" value="${optionIndex}" class="sr-only">
                                <div class="quiz-option-indicator"></div>
                                <span class="text-gray-900">${option}</span>
                            </label>
                        `).join('')}
                    </div>
                `;
                
            case 'true_false':
                return `
                    <div class="space-y-3">
                        <label class="quiz-option ${this.answers[questionIndex] === true ? 'selected' : ''}" 
                               onclick="quizzesManager.selectAnswer(${questionIndex}, true)">
                            <input type="radio" name="question-${questionIndex}" value="true" class="sr-only">
                            <div class="quiz-option-indicator"></div>
                            <span class="text-gray-900">صحيح</span>
                        </label>
                        <label class="quiz-option ${this.answers[questionIndex] === false ? 'selected' : ''}" 
                               onclick="quizzesManager.selectAnswer(${questionIndex}, false)">
                            <input type="radio" name="question-${questionIndex}" value="false" class="sr-only">
                            <div class="quiz-option-indicator"></div>
                            <span class="text-gray-900">خطأ</span>
                        </label>
                    </div>
                `;
                
            case 'short_answer':
                return `
                    <div>
                        <textarea class="form-input w-full h-24 resize-none" 
                                  placeholder="اكتب إجابتك هنا..."
                                  onchange="quizzesManager.selectAnswer(${questionIndex}, this.value)"
                                  >${this.answers[questionIndex] || ''}</textarea>
                    </div>
                `;
                
            default:
                return '<p class="text-red-500">نوع سؤال غير مدعوم</p>';
        }
    }

    // اختيار إجابة
    selectAnswer(questionIndex, answer) {
        this.answers[questionIndex] = answer;
        
        // تحديث واجهة المستخدم
        const questionElement = document.getElementById(`question-${questionIndex}`);
        if (questionElement) {
            // إزالة التحديد السابق
            questionElement.querySelectorAll('.quiz-option').forEach(option => {
                option.classList.remove('selected');
            });
            
            // إضافة التحديد الجديد
            if (typeof answer === 'number') {
                const selectedOption = questionElement.querySelectorAll('.quiz-option')[answer];
                if (selectedOption) {
                    selectedOption.classList.add('selected');
                }
            } else if (typeof answer === 'boolean') {
                const selectedOption = questionElement.querySelector(`[onclick*="${answer}"]`);
                if (selectedOption) {
                    selectedOption.classList.add('selected');
                }
            }
        }
        
        // تحديث عداد الإجابات
        this.updateAnsweredCount();
    }

    // تحديث عداد الإجابات
    updateAnsweredCount() {
        const countElement = document.querySelector('.text-sm.text-gray-500');
        if (countElement) {
            countElement.textContent = `تم الإجابة على ${this.getAnsweredCount()} من ${this.currentQuiz.questions.length} أسئلة`;
        }
    }

    // الحصول على عدد الإجابات
    getAnsweredCount() {
        return this.answers.filter(answer => answer !== null && answer !== '').length;
    }

    // بدء المؤقت
    startTimer(seconds) {
        this.timeRemaining = seconds;
        this.updateTimerDisplay();
        
        this.timer = setInterval(() => {
            this.timeRemaining--;
            this.updateTimerDisplay();
            
            if (this.timeRemaining <= 0) {
                this.timeUp();
            }
        }, 1000);
    }

    // تحديث عرض المؤقت
    updateTimerDisplay() {
        const timerElement = document.getElementById('timer');
        if (timerElement) {
            const minutes = Math.floor(this.timeRemaining / 60);
            const seconds = this.timeRemaining % 60;
            timerElement.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            
            // تغيير اللون عند اقتراب انتهاء الوقت
            if (this.timeRemaining <= 300) { // 5 دقائق
                timerElement.classList.add('text-red-600');
            } else if (this.timeRemaining <= 600) { // 10 دقائق
                timerElement.classList.add('text-yellow-600');
            }
        }
    }

    // انتهاء الوقت
    timeUp() {
        clearInterval(this.timer);
        showToast('انتهى الوقت المحدد للاختبار', 'warning');
        this.submitQuiz(true); // تسليم تلقائي
    }

    // تسليم الاختبار
    async submitQuiz(autoSubmit = false) {
        try {
            // التأكد من الرغبة في التسليم
            if (!autoSubmit) {
                const unanswered = this.currentQuiz.questions.length - this.getAnsweredCount();
                if (unanswered > 0) {
                    const confirmed = confirm(`لم تجب على ${unanswered} أسئلة. هل تريد التسليم؟`);
                    if (!confirmed) return;
                }
            }

            showLoading();
            
            // إيقاف المؤقت
            if (this.timer) {
                clearInterval(this.timer);
            }
            
            // حساب الوقت المستغرق
            const timeTaken = this.currentQuiz.timeLimit ? 
                (this.currentQuiz.timeLimit * 60) - this.timeRemaining : null;
            
            // إرسال الإجابات
            const response = await window.api.submitQuiz(
                this.currentQuiz.id, 
                this.answers, 
                timeTaken
            );
            
            this.currentAttempt = response.data.attempt;
            this.showQuizResults();
            
        } catch (error) {
            console.error('خطأ في تسليم الاختبار:', error);
            showToast('فشل في تسليم الاختبار', 'error');
        } finally {
            hideLoading();
        }
    }

    // عرض نتائج الاختبار
    showQuizResults() {
        const container = document.getElementById('dashboard-container');
        
        const passed = this.currentAttempt.passed;
        const score = this.currentAttempt.score;
        
        const html = `
            <div class="min-h-screen bg-gray-50 flex items-center justify-center">
                <div class="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
                    <div class="mb-6">
                        <div class="w-20 h-20 mx-auto mb-4 rounded-full flex items-center justify-center ${passed ? 'bg-green-100' : 'bg-red-100'}">
                            <i data-feather="${passed ? 'check-circle' : 'x-circle'}" class="h-10 w-10 ${passed ? 'text-green-500' : 'text-red-500'}"></i>
                        </div>
                        <h2 class="text-2xl font-bold ${passed ? 'text-green-600' : 'text-red-600'} mb-2">
                            ${passed ? 'مبروك! لقد نجحت' : 'للأسف، لم تنجح'}
                        </h2>
                        <p class="text-gray-600">في اختبار: ${this.currentQuiz.title}</p>
                    </div>
                    
                    <div class="space-y-4 mb-6">
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600">النتيجة:</span>
                            <span class="text-2xl font-bold ${passed ? 'text-green-600' : 'text-red-600'}">${score}%</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600">درجة النجاح:</span>
                            <span class="font-medium">${this.currentQuiz.passingScore}%</span>
                        </div>
                        ${this.currentAttempt.timeTaken ? `
                            <div class="flex justify-between items-center">
                                <span class="text-gray-600">الوقت المستغرق:</span>
                                <span class="font-medium">${this.formatTime(this.currentAttempt.timeTaken)}</span>
                            </div>
                        ` : ''}
                    </div>
                    
                    <div class="space-y-3">
                        <button class="btn btn-primary w-full" onclick="quizzesManager.loadQuizzes()">
                            العودة للاختبارات
                        </button>
                        ${!passed && this.canRetakeQuiz() ? `
                            <button class="btn btn-secondary w-full" onclick="quizzesManager.startQuiz('${this.currentQuiz.id}')">
                                إعادة المحاولة
                            </button>
                        ` : ''}
                    </div>
                </div>
            </div>
        `;

        container.innerHTML = html;
        feather.replace();
    }

    // التحقق من إمكانية إعادة المحاولة
    canRetakeQuiz() {
        // هذا يحتاج لتطبيق منطق التحقق من عدد المحاولات
        return true; // مؤقت
    }

    // الخروج من الاختبار
    exitQuiz() {
        if (confirm('هل أنت متأكد من الخروج؟ ستفقد جميع إجاباتك.')) {
            if (this.timer) {
                clearInterval(this.timer);
            }
            this.loadQuizzes();
        }
    }

    // عرض المحاولات السابقة
    async viewAttempts(quizId) {
        try {
            showLoading();
            const response = await window.api.getQuizAttempts(quizId);
            // عرض المحاولات في نافذة منبثقة
            showToast('عرض المحاولات قريباً!', 'info');
        } catch (error) {
            console.error('خطأ في جلب المحاولات:', error);
            showToast('فشل في جلب المحاولات', 'error');
        } finally {
            hideLoading();
        }
    }

    // عرض الإحصائيات
    async viewStatistics(quizId) {
        try {
            showLoading();
            const response = await window.api.getQuizStatistics(quizId);
            // عرض الإحصائيات
            showToast('عرض الإحصائيات قريباً!', 'info');
        } catch (error) {
            console.error('خطأ في جلب الإحصائيات:', error);
            showToast('فشل في جلب الإحصائيات', 'error');
        } finally {
            hideLoading();
        }
    }

    // إنشاء اختبار جديد
    showCreateQuizModal() {
        showToast('ميزة إنشاء الاختبارات قريباً!', 'info');
    }

    // تعديل اختبار
    editQuiz(quizId) {
        showToast('ميزة تعديل الاختبارات قريباً!', 'info');
    }

    // حذف اختبار
    async deleteQuiz(quizId) {
        if (!confirm('هل أنت متأكد من حذف هذا الاختبار؟')) return;
        
        try {
            showLoading();
            await window.api.deleteQuiz(quizId);
            showToast('تم حذف الاختبار بنجاح', 'success');
            this.loadQuizzes();
        } catch (error) {
            console.error('خطأ في حذف الاختبار:', error);
            showToast('فشل في حذف الاختبار', 'error');
        } finally {
            hideLoading();
        }
    }

    // دوال مساعدة
    getCurrentCourseId() {
        // يمكن الحصول عليه من الرابط أو المتغيرات العامة
        return null; // مؤقت
    }

    formatTime(seconds) {
        const minutes = Math.floor(seconds / 60);
        const secs = seconds % 60;
        return `${minutes}:${secs.toString().padStart(2, '0')}`;
    }

    renderEmptyState() {
        return `
            <div class="col-span-full text-center py-12">
                <i data-feather="help-circle" class="h-16 w-16 text-gray-400 mx-auto mb-4"></i>
                <h3 class="text-xl font-medium text-gray-900 mb-2">لا توجد اختبارات</h3>
                <p class="text-gray-600 mb-4">
                    ${this.userRole === 'instructor' ? 'ابدأ بإنشاء اختبارك الأول' : 'لا توجد اختبارات متاحة حالياً'}
                </p>
                ${this.userRole === 'instructor' || this.userRole === 'admin' ? `
                    <button class="btn btn-primary" onclick="quizzesManager.showCreateQuizModal()">
                        إنشاء اختبار جديد
                    </button>
                ` : ''}
            </div>
        `;
    }

    getLoadingHTML() {
        return `
            <div class="flex items-center justify-center h-64">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
            </div>
        `;
    }

    showError(message) {
        const container = document.getElementById('dashboard-container');
        container.innerHTML = `
            <div class="text-center py-12">
                <i data-feather="alert-circle" class="h-12 w-12 text-red-500 mx-auto mb-4"></i>
                <h2 class="text-xl font-bold text-gray-900 mb-2">خطأ</h2>
                <p class="text-gray-600">${message}</p>
                <button class="btn btn-primary mt-4" onclick="quizzesManager.loadQuizzes()">المحاولة مرة أخرى</button>
            </div>
        `;
        feather.replace();
    }
}

// تهيئة مدير الاختبارات
window.quizzesManager = new QuizzesManager();
