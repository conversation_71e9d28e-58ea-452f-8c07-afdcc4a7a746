@echo off
chcp 65001 >nul
echo 🚀 ==========================================
echo 🎓 نشر منصة الدورات التدريبية على Firebase
echo 🚀 ==========================================

REM التحقق من وجود Firebase CLI
firebase --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Firebase CLI غير مثبت
    echo 📥 يرجى تثبيت Firebase CLI أولاً:
    echo npm install -g firebase-tools
    pause
    exit /b 1
)

echo ✅ Firebase CLI موجود

REM التحقق من تسجيل الدخول
echo 🔐 التحقق من تسجيل الدخول...
firebase projects:list >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ يرجى تسجيل الدخول إلى Firebase أولاً:
    echo firebase login
    pause
    exit /b 1
)

echo ✅ تم تسجيل الدخول بنجاح

REM التحقق من المشروع
echo 📋 التحقق من المشروع...
firebase use --project marketwise-academy-qhizq >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ لا يمكن الوصول للمشروع marketwise-academy-qhizq
    echo يرجى التأكد من الصلاحيات
    pause
    exit /b 1
)

echo ✅ تم تحديد المشروع: marketwise-academy-qhizq

REM تثبيت التبعيات للـ Functions
echo 📦 تثبيت تبعيات Firebase Functions...
cd backend
if exist "package.json" (
    npm install
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت التبعيات
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت التبعيات بنجاح
) else (
    echo ❌ ملف package.json غير موجود في مجلد backend
    pause
    exit /b 1
)

cd ..

REM بناء المشروع
echo 🔨 بناء المشروع...
cd backend
npm run build
if %errorlevel% neq 0 (
    echo ❌ فشل في بناء المشروع
    pause
    exit /b 1
)
cd ..

echo ✅ تم بناء المشروع بنجاح

REM نشر المشروع
echo 🚀 نشر المشروع على Firebase...
firebase deploy

if %errorlevel% equ 0 (
    echo 🚀 ==========================================
    echo ✅ تم النشر بنجاح!
    echo 🌐 الرابط: https://marketwise-academy-qhizq.web.app
    echo 📊 لوحة التحكم: https://console.firebase.google.com/project/marketwise-academy-qhizq
    echo 🚀 ==========================================
) else (
    echo ❌ فشل في النشر
    echo يرجى مراجعة الأخطاء أعلاه
)

pause
