-- قواعد الأمان لقاعدة البيانات (Row Level Security)

-- تفعيل RLS على جميع الجداول
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE courses ENABLE ROW LEVEL SECURITY;
ALTER TABLE course_sections ENABLE ROW LEVEL SECURITY;
ALTER TABLE videos ENABLE ROW LEVEL SECURITY;
ALTER TABLE pdfs ENABLE ROW LEVEL SECURITY;
ALTER TABLE quizzes ENABLE ROW LEVEL SECURITY;
ALTER TABLE course_enrollments ENABLE ROW LEVEL SECURITY;
ALTER TABLE video_progress ENABLE ROW LEVEL SECURITY;
ALTER TABLE quiz_attempts ENABLE ROW LEVEL SECURITY;
ALTER TABLE certificates ENABLE ROW LEVEL SECURITY;
ALTER TABLE course_reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;

-- قواعد جدول المستخدمين
-- المستخدمون يمكنهم قراءة ملفاتهم الشخصية فقط
CREATE POLICY "Users can view own profile" ON users
    FOR SELECT USING (auth.uid()::text = firebase_uid);

-- المستخدمون يمكنهم تحديث ملفاتهم الشخصية فقط
CREATE POLICY "Users can update own profile" ON users
    FOR UPDATE USING (auth.uid()::text = firebase_uid);

-- الإداريون يمكنهم رؤية جميع المستخدمين
CREATE POLICY "Admins can view all users" ON users
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE firebase_uid = auth.uid()::text 
            AND role = 'admin'
        )
    );

-- قواعد جدول الدورات
-- الجميع يمكنهم رؤية الدورات المنشورة
CREATE POLICY "Anyone can view published courses" ON courses
    FOR SELECT USING (is_published = true);

-- المدربون يمكنهم رؤية دوراتهم الخاصة
CREATE POLICY "Instructors can view own courses" ON courses
    FOR SELECT USING (
        instructor_id IN (
            SELECT id FROM users 
            WHERE firebase_uid = auth.uid()::text
        )
    );

-- المدربون يمكنهم إنشاء دورات
CREATE POLICY "Instructors can create courses" ON courses
    FOR INSERT WITH CHECK (
        instructor_id IN (
            SELECT id FROM users 
            WHERE firebase_uid = auth.uid()::text 
            AND role IN ('instructor', 'admin')
        )
    );

-- المدربون يمكنهم تحديث دوراتهم
CREATE POLICY "Instructors can update own courses" ON courses
    FOR UPDATE USING (
        instructor_id IN (
            SELECT id FROM users 
            WHERE firebase_uid = auth.uid()::text
        )
    );

-- المدربون يمكنهم حذف دوراتهم
CREATE POLICY "Instructors can delete own courses" ON courses
    FOR DELETE USING (
        instructor_id IN (
            SELECT id FROM users 
            WHERE firebase_uid = auth.uid()::text
        )
    );

-- قواعد أقسام الدورة
-- الجميع يمكنهم رؤية أقسام الدورات المنشورة
CREATE POLICY "Anyone can view sections of published courses" ON course_sections
    FOR SELECT USING (
        course_id IN (
            SELECT id FROM courses WHERE is_published = true
        )
    );

-- المدربون يمكنهم إدارة أقسام دوراتهم
CREATE POLICY "Instructors can manage own course sections" ON course_sections
    FOR ALL USING (
        course_id IN (
            SELECT c.id FROM courses c
            JOIN users u ON c.instructor_id = u.id
            WHERE u.firebase_uid = auth.uid()::text
        )
    );

-- قواعد الفيديوهات
-- الطلاب المسجلون يمكنهم رؤية الفيديوهات
CREATE POLICY "Enrolled students can view videos" ON videos
    FOR SELECT USING (
        section_id IN (
            SELECT cs.id FROM course_sections cs
            JOIN courses c ON cs.course_id = c.id
            JOIN course_enrollments ce ON c.id = ce.course_id
            JOIN users u ON ce.student_id = u.id
            WHERE u.firebase_uid = auth.uid()::text
        )
        OR 
        -- أو الفيديوهات المجانية للمعاينة
        (is_preview = true AND section_id IN (
            SELECT cs.id FROM course_sections cs
            JOIN courses c ON cs.course_id = c.id
            WHERE c.is_published = true
        ))
    );

-- المدربون يمكنهم إدارة فيديوهات دوراتهم
CREATE POLICY "Instructors can manage own course videos" ON videos
    FOR ALL USING (
        section_id IN (
            SELECT cs.id FROM course_sections cs
            JOIN courses c ON cs.course_id = c.id
            JOIN users u ON c.instructor_id = u.id
            WHERE u.firebase_uid = auth.uid()::text
        )
    );

-- قواعد ملفات PDF
-- الطلاب المسجلون يمكنهم رؤية ملفات PDF
CREATE POLICY "Enrolled students can view PDFs" ON pdfs
    FOR SELECT USING (
        video_id IN (
            SELECT v.id FROM videos v
            JOIN course_sections cs ON v.section_id = cs.id
            JOIN courses c ON cs.course_id = c.id
            JOIN course_enrollments ce ON c.id = ce.course_id
            JOIN users u ON ce.student_id = u.id
            WHERE u.firebase_uid = auth.uid()::text
        )
    );

-- المدربون يمكنهم إدارة ملفات PDF لدوراتهم
CREATE POLICY "Instructors can manage PDFs for own courses" ON pdfs
    FOR ALL USING (
        video_id IN (
            SELECT v.id FROM videos v
            JOIN course_sections cs ON v.section_id = cs.id
            JOIN courses c ON cs.course_id = c.id
            JOIN users u ON c.instructor_id = u.id
            WHERE u.firebase_uid = auth.uid()::text
        )
    );

-- قواعد الاختبارات
-- الطلاب المسجلون يمكنهم رؤية الاختبارات النشطة
CREATE POLICY "Enrolled students can view active quizzes" ON quizzes
    FOR SELECT USING (
        is_active = true AND
        course_id IN (
            SELECT ce.course_id FROM course_enrollments ce
            JOIN users u ON ce.student_id = u.id
            WHERE u.firebase_uid = auth.uid()::text
        )
    );

-- المدربون يمكنهم إدارة اختبارات دوراتهم
CREATE POLICY "Instructors can manage own course quizzes" ON quizzes
    FOR ALL USING (
        course_id IN (
            SELECT c.id FROM courses c
            JOIN users u ON c.instructor_id = u.id
            WHERE u.firebase_uid = auth.uid()::text
        )
    );

-- قواعد التسجيل في الدورات
-- الطلاب يمكنهم رؤية تسجيلاتهم
CREATE POLICY "Students can view own enrollments" ON course_enrollments
    FOR SELECT USING (
        student_id IN (
            SELECT id FROM users 
            WHERE firebase_uid = auth.uid()::text
        )
    );

-- المدربون يمكنهم رؤية تسجيلات دوراتهم
CREATE POLICY "Instructors can view own course enrollments" ON course_enrollments
    FOR SELECT USING (
        course_id IN (
            SELECT c.id FROM courses c
            JOIN users u ON c.instructor_id = u.id
            WHERE u.firebase_uid = auth.uid()::text
        )
    );

-- الطلاب يمكنهم التسجيل في الدورات
CREATE POLICY "Students can enroll in courses" ON course_enrollments
    FOR INSERT WITH CHECK (
        student_id IN (
            SELECT id FROM users 
            WHERE firebase_uid = auth.uid()::text 
            AND role = 'student'
        )
    );

-- قواعد تقدم الفيديوهات
-- الطلاب يمكنهم رؤية وتحديث تقدمهم
CREATE POLICY "Students can manage own video progress" ON video_progress
    FOR ALL USING (
        student_id IN (
            SELECT id FROM users 
            WHERE firebase_uid = auth.uid()::text
        )
    );

-- قواعد محاولات الاختبارات
-- الطلاب يمكنهم رؤية محاولاتهم
CREATE POLICY "Students can view own quiz attempts" ON quiz_attempts
    FOR SELECT USING (
        student_id IN (
            SELECT id FROM users 
            WHERE firebase_uid = auth.uid()::text
        )
    );

-- الطلاب يمكنهم إنشاء محاولات جديدة
CREATE POLICY "Students can create quiz attempts" ON quiz_attempts
    FOR INSERT WITH CHECK (
        student_id IN (
            SELECT id FROM users 
            WHERE firebase_uid = auth.uid()::text
        )
    );

-- المدربون يمكنهم رؤية محاولات اختبارات دوراتهم
CREATE POLICY "Instructors can view attempts for own course quizzes" ON quiz_attempts
    FOR SELECT USING (
        quiz_id IN (
            SELECT q.id FROM quizzes q
            JOIN courses c ON q.course_id = c.id
            JOIN users u ON c.instructor_id = u.id
            WHERE u.firebase_uid = auth.uid()::text
        )
    );

-- قواعد الشهادات
-- الطلاب يمكنهم رؤية شهاداتهم
CREATE POLICY "Students can view own certificates" ON certificates
    FOR SELECT USING (
        student_id IN (
            SELECT id FROM users 
            WHERE firebase_uid = auth.uid()::text
        )
    );

-- قواعد المراجعات
-- الجميع يمكنهم رؤية المراجعات للدورات المنشورة
CREATE POLICY "Anyone can view reviews for published courses" ON course_reviews
    FOR SELECT USING (
        course_id IN (
            SELECT id FROM courses WHERE is_published = true
        )
    );

-- الطلاب المسجلون يمكنهم كتابة مراجعات
CREATE POLICY "Enrolled students can create reviews" ON course_reviews
    FOR INSERT WITH CHECK (
        student_id IN (
            SELECT id FROM users 
            WHERE firebase_uid = auth.uid()::text
        ) AND
        course_id IN (
            SELECT ce.course_id FROM course_enrollments ce
            JOIN users u ON ce.student_id = u.id
            WHERE u.firebase_uid = auth.uid()::text
        )
    );

-- الطلاب يمكنهم تحديث مراجعاتهم
CREATE POLICY "Students can update own reviews" ON course_reviews
    FOR UPDATE USING (
        student_id IN (
            SELECT id FROM users 
            WHERE firebase_uid = auth.uid()::text
        )
    );

-- قواعد الإشعارات
-- المستخدمون يمكنهم رؤية إشعاراتهم فقط
CREATE POLICY "Users can view own notifications" ON notifications
    FOR SELECT USING (
        user_id IN (
            SELECT id FROM users 
            WHERE firebase_uid = auth.uid()::text
        )
    );

-- المستخدمون يمكنهم تحديث إشعاراتهم (تعليم كمقروءة)
CREATE POLICY "Users can update own notifications" ON notifications
    FOR UPDATE USING (
        user_id IN (
            SELECT id FROM users 
            WHERE firebase_uid = auth.uid()::text
        )
    );
