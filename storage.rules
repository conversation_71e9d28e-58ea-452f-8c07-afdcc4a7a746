rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // User avatars - users can upload their own avatars
    match /avatars/{userId}/{allPaths=**} {
      allow read: if true; // Public read access for avatars
      allow write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Course thumbnails - instructors and admins can upload
    match /course-thumbnails/{allPaths=**} {
      allow read: if true; // Public read access for course thumbnails
      allow write: if request.auth != null && 
        (request.auth.token.role == 'instructor' || request.auth.token.role == 'admin');
    }
    
    // Temporary uploads - authenticated users only
    match /temp/{userId}/{allPaths=**} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Default deny all other access
    match /{allPaths=**} {
      allow read, write: if false;
    }
  }
}
