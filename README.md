# Online Courses Platform

A professional online courses platform built with Node.js, Express.js, Firebase, and Supabase.

## Features

- **Authentication System**: Firebase Authentication with role-based access control
- **Admin Dashboard**: Course management, video uploads, quiz creation, certificate generation
- **Student Dashboard**: Course viewing, progress tracking, quiz taking, certificate download
- **Real-time Updates**: Live content delivery and progress synchronization
- **File Storage**: Video and PDF uploads with Supabase Storage
- **Responsive Design**: Mobile-first design with blue/white color scheme

## Technology Stack

- **Backend**: Node.js with Express.js
- **Frontend**: HTML5, CSS3, JavaScript with Tailwind CSS
- **Database**: Firebase Firestore + Supabase PostgreSQL
- **Storage**: Supabase Storage
- **Authentication**: Firebase Authentication
- **Hosting**: Firebase Hosting

## Quick Start

### Prerequisites

- Node.js (v16 or higher)
- npm (v8 or higher)
- Firebase CLI
- Supabase account
- Firebase project

### Installation

1. **Clone and install dependencies:**
   ```bash
   npm run install:all
   ```

2. **Configure environment variables:**
   ```bash
   cp backend/.env.example backend/.env
   # Edit backend/.env with your Firebase and Supabase credentials
   ```

3. **Update Firebase configuration:**
   ```bash
   # Edit frontend/src/config/firebase.js with your Firebase config
   ```

4. **Setup database:**
   ```bash
   npm run setup:db
   ```

5. **Start development servers:**
   ```bash
   npm run dev
   ```

### Environment Variables

Copy `backend/.env.example` to `backend/.env` and fill in your credentials:

- **Firebase**: Project ID, private key, client email, etc.
- **Supabase**: URL, anon key, service role key
- **JWT**: Secret key for token signing
- **File Upload**: Size limits and paths

### Database Setup

The platform uses both Firebase Firestore and Supabase:

- **Firebase Firestore**: Real-time data and authentication
- **Supabase**: Relational data and file storage

Run `npm run setup:db` to automatically create all required tables and storage buckets.

### Deployment

1. **Build the application:**
   ```bash
   npm run build
   ```

2. **Deploy to Firebase:**
   ```bash
   npm run deploy
   ```

## Project Structure

```
├── backend/                 # Express.js API server
│   ├── controllers/         # Route controllers
│   ├── middleware/          # Custom middleware
│   ├── models/             # Database models
│   ├── routes/             # API routes
│   ├── scripts/            # Database setup scripts
│   ├── utils/              # Utility functions
│   └── server.js           # Main server file
├── frontend/               # Frontend static files
│   ├── src/                # Source files
│   │   ├── css/            # Stylesheets
│   │   ├── js/             # JavaScript modules
│   │   └── config/         # Configuration files
│   ├── dist/               # Built files
│   └── index.html          # Main HTML file
├── firebase.json           # Firebase configuration
└── package.json            # Root package configuration
```

## API Endpoints

### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/register` - User registration
- `POST /api/auth/logout` - User logout

### Courses
- `GET /api/courses` - Get all courses
- `POST /api/courses` - Create new course
- `PUT /api/courses/:id` - Update course
- `DELETE /api/courses/:id` - Delete course

### Videos
- `GET /api/videos/:courseId` - Get course videos
- `POST /api/videos` - Upload new video
- `PUT /api/videos/:id` - Update video
- `DELETE /api/videos/:id` - Delete video

### Quizzes
- `GET /api/quizzes/:courseId` - Get course quizzes
- `POST /api/quizzes` - Create new quiz
- `POST /api/quizzes/:id/submit` - Submit quiz attempt

### File Uploads
- `POST /api/upload/video` - Upload video file
- `POST /api/upload/pdf` - Upload PDF file

## User Roles

1. **Student**: View assigned courses, watch videos, take quizzes, download certificates
2. **Instructor**: Create courses, upload content, manage students, view analytics
3. **Admin**: Full system access, user management, system configuration

## Support

For support and questions, please refer to the documentation or create an issue in the repository.

## License

MIT License - see LICENSE file for details.
