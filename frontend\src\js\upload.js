// وحدة رفع الملفات
class UploadManager {
    constructor() {
        this.activeUploads = new Map();
        this.maxConcurrentUploads = 3;
        this.uploadQueue = [];
    }

    // رفع ملف فيديو
    async uploadVideo(file, onProgress, onComplete, onError) {
        try {
            // التحقق من صحة الملف
            if (!this.validateVideoFile(file)) {
                throw new Error('نوع الملف غير مدعوم. يرجى رفع ملف فيديو صالح.');
            }

            const uploadId = this.generateUploadId();
            
            // إضافة إلى قائمة الرفع النشطة
            this.activeUploads.set(uploadId, {
                file,
                type: 'video',
                progress: 0,
                status: 'uploading'
            });

            // إنشاء شريط التقدم
            this.createProgressIndicator(uploadId, file.name, 'video');

            // رفع الملف
            const response = await window.api.uploadVideo(file, (progress) => {
                this.updateProgress(uploadId, progress);
                if (onProgress) onProgress(progress);
            });

            // تحديث الحالة عند الانتهاء
            this.activeUploads.get(uploadId).status = 'completed';
            this.updateProgressIndicator(uploadId, 'completed');

            if (onComplete) onComplete(response);
            
            // إزالة من قائمة الرفع بعد 3 ثوان
            setTimeout(() => {
                this.removeProgressIndicator(uploadId);
                this.activeUploads.delete(uploadId);
            }, 3000);

            return response;

        } catch (error) {
            console.error('خطأ في رفع الفيديو:', error);
            
            if (uploadId) {
                this.activeUploads.get(uploadId).status = 'error';
                this.updateProgressIndicator(uploadId, 'error', error.message);
            }
            
            if (onError) onError(error);
            throw error;
        }
    }

    // رفع ملف PDF
    async uploadPdf(file, onProgress, onComplete, onError) {
        try {
            // التحقق من صحة الملف
            if (!this.validatePdfFile(file)) {
                throw new Error('يجب أن يكون الملف من نوع PDF.');
            }

            const uploadId = this.generateUploadId();
            
            this.activeUploads.set(uploadId, {
                file,
                type: 'pdf',
                progress: 0,
                status: 'uploading'
            });

            this.createProgressIndicator(uploadId, file.name, 'pdf');

            const response = await window.api.uploadPdf(file, (progress) => {
                this.updateProgress(uploadId, progress);
                if (onProgress) onProgress(progress);
            });

            this.activeUploads.get(uploadId).status = 'completed';
            this.updateProgressIndicator(uploadId, 'completed');

            if (onComplete) onComplete(response);
            
            setTimeout(() => {
                this.removeProgressIndicator(uploadId);
                this.activeUploads.delete(uploadId);
            }, 3000);

            return response;

        } catch (error) {
            console.error('خطأ في رفع PDF:', error);
            
            if (uploadId) {
                this.activeUploads.get(uploadId).status = 'error';
                this.updateProgressIndicator(uploadId, 'error', error.message);
            }
            
            if (onError) onError(error);
            throw error;
        }
    }

    // رفع صورة
    async uploadImage(file, type = 'general', onProgress, onComplete, onError) {
        try {
            if (!this.validateImageFile(file)) {
                throw new Error('نوع الصورة غير مدعوم. يرجى رفع صورة صالحة.');
            }

            const uploadId = this.generateUploadId();
            
            this.activeUploads.set(uploadId, {
                file,
                type: 'image',
                progress: 0,
                status: 'uploading'
            });

            this.createProgressIndicator(uploadId, file.name, 'image');

            const response = await window.api.uploadImage(file, type, (progress) => {
                this.updateProgress(uploadId, progress);
                if (onProgress) onProgress(progress);
            });

            this.activeUploads.get(uploadId).status = 'completed';
            this.updateProgressIndicator(uploadId, 'completed');

            if (onComplete) onComplete(response);
            
            setTimeout(() => {
                this.removeProgressIndicator(uploadId);
                this.activeUploads.delete(uploadId);
            }, 3000);

            return response;

        } catch (error) {
            console.error('خطأ في رفع الصورة:', error);
            
            if (uploadId) {
                this.activeUploads.get(uploadId).status = 'error';
                this.updateProgressIndicator(uploadId, 'error', error.message);
            }
            
            if (onError) onError(error);
            throw error;
        }
    }

    // التحقق من صحة ملف الفيديو
    validateVideoFile(file) {
        const allowedTypes = ['video/mp4', 'video/webm', 'video/ogg'];
        const maxSize = appConfig.ui.maxFileSize.video;

        if (!allowedTypes.includes(file.type)) {
            showToast('نوع الفيديو غير مدعوم. الأنواع المدعومة: MP4, WebM, OGG', 'error');
            return false;
        }

        if (file.size > maxSize) {
            showToast(`حجم الفيديو كبير جداً. الحد الأقصى: ${formatFileSize(maxSize)}`, 'error');
            return false;
        }

        return true;
    }

    // التحقق من صحة ملف PDF
    validatePdfFile(file) {
        const maxSize = appConfig.ui.maxFileSize.pdf;

        if (file.type !== 'application/pdf') {
            showToast('يجب أن يكون الملف من نوع PDF', 'error');
            return false;
        }

        if (file.size > maxSize) {
            showToast(`حجم الملف كبير جداً. الحد الأقصى: ${formatFileSize(maxSize)}`, 'error');
            return false;
        }

        return true;
    }

    // التحقق من صحة ملف الصورة
    validateImageFile(file) {
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
        const maxSize = appConfig.ui.maxFileSize.image;

        if (!allowedTypes.includes(file.type)) {
            showToast('نوع الصورة غير مدعوم. الأنواع المدعومة: JPG, PNG, GIF, WebP', 'error');
            return false;
        }

        if (file.size > maxSize) {
            showToast(`حجم الصورة كبير جداً. الحد الأقصى: ${formatFileSize(maxSize)}`, 'error');
            return false;
        }

        return true;
    }

    // إنشاء مؤشر التقدم
    createProgressIndicator(uploadId, fileName, fileType) {
        const container = this.getOrCreateUploadContainer();
        
        const progressElement = document.createElement('div');
        progressElement.id = `upload-${uploadId}`;
        progressElement.className = 'upload-progress-item bg-white border border-gray-200 rounded-lg p-4 mb-3 shadow-sm';
        
        const iconMap = {
            video: 'video',
            pdf: 'file-text',
            image: 'image'
        };

        progressElement.innerHTML = `
            <div class="flex items-center space-x-3">
                <div class="flex-shrink-0">
                    <i data-feather="${iconMap[fileType]}" class="h-6 w-6 text-gray-400"></i>
                </div>
                <div class="flex-1 min-w-0">
                    <p class="text-sm font-medium text-gray-900 truncate">${fileName}</p>
                    <div class="mt-2">
                        <div class="bg-gray-200 rounded-full h-2">
                            <div class="bg-primary-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                        </div>
                        <p class="text-xs text-gray-500 mt-1">جاري الرفع... 0%</p>
                    </div>
                </div>
                <div class="flex-shrink-0">
                    <button class="text-gray-400 hover:text-gray-600" onclick="uploadManager.cancelUpload('${uploadId}')">
                        <i data-feather="x" class="h-5 w-5"></i>
                    </button>
                </div>
            </div>
        `;

        container.appendChild(progressElement);
        feather.replace();
    }

    // تحديث مؤشر التقدم
    updateProgress(uploadId, progress) {
        const element = document.getElementById(`upload-${uploadId}`);
        if (!element) return;

        const progressBar = element.querySelector('.bg-primary-600');
        const progressText = element.querySelector('.text-xs');

        if (progressBar) {
            progressBar.style.width = `${progress}%`;
        }

        if (progressText) {
            progressText.textContent = `جاري الرفع... ${Math.round(progress)}%`;
        }

        // تحديث البيانات المحفوظة
        if (this.activeUploads.has(uploadId)) {
            this.activeUploads.get(uploadId).progress = progress;
        }
    }

    // تحديث مؤشر التقدم عند الانتهاء أو الخطأ
    updateProgressIndicator(uploadId, status, errorMessage = '') {
        const element = document.getElementById(`upload-${uploadId}`);
        if (!element) return;

        const progressBar = element.querySelector('.bg-primary-600');
        const progressText = element.querySelector('.text-xs');
        const icon = element.querySelector('i[data-feather]');

        if (status === 'completed') {
            if (progressBar) {
                progressBar.style.width = '100%';
                progressBar.className = progressBar.className.replace('bg-primary-600', 'bg-green-500');
            }
            if (progressText) {
                progressText.textContent = 'تم الرفع بنجاح!';
                progressText.className = progressText.className.replace('text-gray-500', 'text-green-600');
            }
            if (icon) {
                icon.setAttribute('data-feather', 'check-circle');
                icon.className = icon.className.replace('text-gray-400', 'text-green-500');
            }
        } else if (status === 'error') {
            if (progressBar) {
                progressBar.className = progressBar.className.replace('bg-primary-600', 'bg-red-500');
            }
            if (progressText) {
                progressText.textContent = errorMessage || 'فشل في الرفع';
                progressText.className = progressText.className.replace('text-gray-500', 'text-red-600');
            }
            if (icon) {
                icon.setAttribute('data-feather', 'x-circle');
                icon.className = icon.className.replace('text-gray-400', 'text-red-500');
            }
        }

        feather.replace();
    }

    // إزالة مؤشر التقدم
    removeProgressIndicator(uploadId) {
        const element = document.getElementById(`upload-${uploadId}`);
        if (element) {
            element.style.opacity = '0';
            element.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (element.parentNode) {
                    element.parentNode.removeChild(element);
                }
            }, 300);
        }
    }

    // إلغاء الرفع
    cancelUpload(uploadId) {
        if (this.activeUploads.has(uploadId)) {
            this.activeUploads.delete(uploadId);
            this.removeProgressIndicator(uploadId);
            showToast('تم إلغاء الرفع', 'info');
        }
    }

    // الحصول على أو إنشاء حاوية الرفع
    getOrCreateUploadContainer() {
        let container = document.getElementById('upload-progress-container');
        
        if (!container) {
            container = document.createElement('div');
            container.id = 'upload-progress-container';
            container.className = 'fixed bottom-4 right-4 w-80 max-h-96 overflow-y-auto z-40';
            document.body.appendChild(container);
        }

        return container;
    }

    // توليد معرف فريد للرفع
    generateUploadId() {
        return 'upload_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
    }

    // إنشاء منطقة السحب والإفلات
    createDropZone(containerId, fileTypes = ['video'], onFilesSelected) {
        const container = document.getElementById(containerId);
        if (!container) return;

        container.innerHTML = `
            <div class="drop-zone border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-primary-500 transition-colors duration-200">
                <div class="space-y-4">
                    <div class="mx-auto h-12 w-12 text-gray-400">
                        <i data-feather="upload-cloud" class="h-12 w-12"></i>
                    </div>
                    <div>
                        <p class="text-lg font-medium text-gray-900">اسحب الملفات هنا</p>
                        <p class="text-sm text-gray-500">أو انقر للاختيار</p>
                    </div>
                    <div class="text-xs text-gray-400">
                        ${this.getFileTypeDescription(fileTypes)}
                    </div>
                </div>
                <input type="file" class="hidden" ${fileTypes.includes('video') ? 'accept="video/*"' : fileTypes.includes('pdf') ? 'accept=".pdf"' : 'accept="image/*"'} ${fileTypes.length > 1 ? 'multiple' : ''}>
            </div>
        `;

        const dropZone = container.querySelector('.drop-zone');
        const fileInput = container.querySelector('input[type="file"]');

        // أحداث السحب والإفلات
        dropZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            dropZone.classList.add('border-primary-500', 'bg-primary-50');
        });

        dropZone.addEventListener('dragleave', (e) => {
            e.preventDefault();
            dropZone.classList.remove('border-primary-500', 'bg-primary-50');
        });

        dropZone.addEventListener('drop', (e) => {
            e.preventDefault();
            dropZone.classList.remove('border-primary-500', 'bg-primary-50');
            
            const files = Array.from(e.dataTransfer.files);
            if (onFilesSelected) onFilesSelected(files);
        });

        // النقر للاختيار
        dropZone.addEventListener('click', () => {
            fileInput.click();
        });

        fileInput.addEventListener('change', (e) => {
            const files = Array.from(e.target.files);
            if (onFilesSelected) onFilesSelected(files);
        });

        feather.replace();
    }

    // وصف أنواع الملفات المدعومة
    getFileTypeDescription(fileTypes) {
        const descriptions = {
            video: 'ملفات الفيديو: MP4, WebM, OGG (حتى 500 ميجابايت)',
            pdf: 'ملفات PDF (حتى 50 ميجابايت)',
            image: 'ملفات الصور: JPG, PNG, GIF, WebP (حتى 10 ميجابايت)'
        };

        return fileTypes.map(type => descriptions[type]).join(' • ');
    }

    // حذف ملف
    async deleteFile(bucket, filePath) {
        try {
            showLoading();
            await window.api.deleteFile(bucket, filePath);
            showToast('تم حذف الملف بنجاح', 'success');
            return true;
        } catch (error) {
            console.error('خطأ في حذف الملف:', error);
            showToast('فشل في حذف الملف', 'error');
            return false;
        } finally {
            hideLoading();
        }
    }

    // الحصول على قائمة الملفات
    async getFilesList(bucket, folder = '') {
        try {
            const response = await window.api.request(`/upload/files?bucket=${bucket}&folder=${folder}`);
            return response.data.files;
        } catch (error) {
            console.error('خطأ في جلب قائمة الملفات:', error);
            throw error;
        }
    }
}

// تهيئة مدير الرفع
window.uploadManager = new UploadManager();
