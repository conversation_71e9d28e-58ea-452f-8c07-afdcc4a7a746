const Course = require('../models/Course');
const { validationResult } = require('express-validator');

class CourseController {
  // Get all courses
  static async getAllCourses(req, res) {
    try {
      const {
        page = 1,
        limit = 10,
        instructor,
        category,
        published,
        search
      } = req.query;

      const offset = (page - 1) * limit;
      const options = {
        limit: parseInt(limit),
        offset: parseInt(offset),
        instructorId: instructor,
        category,
        published: published !== undefined ? published === 'true' : null
      };

      let courses;
      if (search) {
        courses = await Course.search(search, { 
          limit: parseInt(limit),
          published: options.published 
        });
      } else {
        courses = await Course.findAll(options);
      }

      res.json({
        success: true,
        data: {
          courses: courses.map(course => course.toJSON()),
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total: courses.length
          }
        }
      });

    } catch (error) {
      console.error('Get courses error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch courses'
      });
    }
  }

  // Get course by ID
  static async getCourseById(req, res) {
    try {
      const { id } = req.params;
      const course = await Course.findById(id);

      if (!course) {
        return res.status(404).json({
          success: false,
          message: 'Course not found'
        });
      }

      // Check if user has access to unpublished courses
      if (!course.isPublished && req.user) {
        const canAccess = req.user.role === 'admin' || 
                         req.user.id === course.instructorId;
        
        if (!canAccess) {
          return res.status(403).json({
            success: false,
            message: 'Access denied to unpublished course'
          });
        }
      }

      res.json({
        success: true,
        data: {
          course: course.toJSON()
        }
      });

    } catch (error) {
      console.error('Get course error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch course'
      });
    }
  }

  // Create new course
  static async createCourse(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const courseData = {
        ...req.body,
        instructorId: req.user.id
      };

      const course = await Course.create(courseData);

      res.status(201).json({
        success: true,
        message: 'Course created successfully',
        data: {
          course: course.toJSON()
        }
      });

    } catch (error) {
      console.error('Create course error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to create course'
      });
    }
  }

  // Update course
  static async updateCourse(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { id } = req.params;
      const course = await Course.findById(id);

      if (!course) {
        return res.status(404).json({
          success: false,
          message: 'Course not found'
        });
      }

      // Check ownership
      if (req.user.role !== 'admin' && req.user.id !== course.instructorId) {
        return res.status(403).json({
          success: false,
          message: 'Access denied. You can only update your own courses.'
        });
      }

      await course.update(req.body);

      res.json({
        success: true,
        message: 'Course updated successfully',
        data: {
          course: course.toJSON()
        }
      });

    } catch (error) {
      console.error('Update course error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update course'
      });
    }
  }

  // Delete course
  static async deleteCourse(req, res) {
    try {
      const { id } = req.params;
      const course = await Course.findById(id);

      if (!course) {
        return res.status(404).json({
          success: false,
          message: 'Course not found'
        });
      }

      // Check ownership
      if (req.user.role !== 'admin' && req.user.id !== course.instructorId) {
        return res.status(403).json({
          success: false,
          message: 'Access denied. You can only delete your own courses.'
        });
      }

      await course.delete();

      res.json({
        success: true,
        message: 'Course deleted successfully'
      });

    } catch (error) {
      console.error('Delete course error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to delete course'
      });
    }
  }

  // Get course enrollments
  static async getCourseEnrollments(req, res) {
    try {
      const { id } = req.params;
      const course = await Course.findById(id);

      if (!course) {
        return res.status(404).json({
          success: false,
          message: 'Course not found'
        });
      }

      // Check ownership
      if (req.user.role !== 'admin' && req.user.id !== course.instructorId) {
        return res.status(403).json({
          success: false,
          message: 'Access denied'
        });
      }

      const enrollments = await course.getEnrollments();

      res.json({
        success: true,
        data: {
          enrollments
        }
      });

    } catch (error) {
      console.error('Get enrollments error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch enrollments'
      });
    }
  }

  // Enroll student in course
  static async enrollStudent(req, res) {
    try {
      const { id } = req.params;
      const { studentId } = req.body;

      const course = await Course.findById(id);

      if (!course) {
        return res.status(404).json({
          success: false,
          message: 'Course not found'
        });
      }

      // Check permissions
      if (req.user.role !== 'admin' && req.user.id !== course.instructorId) {
        return res.status(403).json({
          success: false,
          message: 'Access denied'
        });
      }

      const enrollment = await course.enrollStudent(studentId);

      res.status(201).json({
        success: true,
        message: 'Student enrolled successfully',
        data: {
          enrollment
        }
      });

    } catch (error) {
      console.error('Enroll student error:', error);
      
      if (error.code === '23505') { // Unique constraint violation
        return res.status(400).json({
          success: false,
          message: 'Student is already enrolled in this course'
        });
      }

      res.status(500).json({
        success: false,
        message: 'Failed to enroll student'
      });
    }
  }

  // Unenroll student from course
  static async unenrollStudent(req, res) {
    try {
      const { id, studentId } = req.params;
      const course = await Course.findById(id);

      if (!course) {
        return res.status(404).json({
          success: false,
          message: 'Course not found'
        });
      }

      // Check permissions
      if (req.user.role !== 'admin' && req.user.id !== course.instructorId) {
        return res.status(403).json({
          success: false,
          message: 'Access denied'
        });
      }

      await course.unenrollStudent(studentId);

      res.json({
        success: true,
        message: 'Student unenrolled successfully'
      });

    } catch (error) {
      console.error('Unenroll student error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to unenroll student'
      });
    }
  }

  // Get student's course progress
  static async getCourseProgress(req, res) {
    try {
      const { id } = req.params;
      const studentId = req.user.id;

      const course = await Course.findById(id);

      if (!course) {
        return res.status(404).json({
          success: false,
          message: 'Course not found'
        });
      }

      const progress = await course.getProgress(studentId);

      if (!progress) {
        return res.status(404).json({
          success: false,
          message: 'Not enrolled in this course'
        });
      }

      res.json({
        success: true,
        data: {
          progress
        }
      });

    } catch (error) {
      console.error('Get progress error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch progress'
      });
    }
  }

  // Update student's course progress
  static async updateCourseProgress(req, res) {
    try {
      const { id } = req.params;
      const studentId = req.user.id;
      const progressData = req.body;

      const course = await Course.findById(id);

      if (!course) {
        return res.status(404).json({
          success: false,
          message: 'Course not found'
        });
      }

      const progress = await course.updateProgress(studentId, progressData);

      res.json({
        success: true,
        message: 'Progress updated successfully',
        data: {
          progress
        }
      });

    } catch (error) {
      console.error('Update progress error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update progress'
      });
    }
  }
}

module.exports = CourseController;
