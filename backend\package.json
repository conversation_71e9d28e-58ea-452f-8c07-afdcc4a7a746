{"name": "courses-platform-backend", "version": "1.0.0", "description": "Express.js backend for online courses platform", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "build": "echo 'Backend build complete'", "setup:db": "node scripts/setup-database.js", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "express-validator": "^7.0.1", "express-rate-limit": "^7.1.5", "compression": "^1.7.4", "firebase-admin": "^11.11.1", "@supabase/supabase-js": "^2.38.4", "uuid": "^9.0.1", "sharp": "^0.32.6", "pdf-lib": "^1.17.1", "canvas": "^2.11.2", "node-cron": "^3.0.3"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "@types/jest": "^29.5.8"}, "engines": {"node": ">=16.0.0"}}