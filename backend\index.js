const functions = require('firebase-functions');
const express = require('express');
const cors = require('cors');
const path = require('path');

const app = express();

// CORS configuration
app.use(cors({
  origin: true,
  credentials: true
}));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Mock data for development
const mockData = {
  users: [
    {
      id: '1',
      email: '<EMAIL>',
      firstName: 'مدير',
      lastName: 'النظام',
      role: 'admin',
      isActive: true,
      avatarUrl: 'https://via.placeholder.com/40',
      createdAt: new Date().toISOString()
    },
    {
      id: '2',
      email: '<EMAIL>',
      firstName: 'أحمد',
      lastName: 'محمد',
      role: 'instructor',
      isActive: true,
      avatarUrl: 'https://via.placeholder.com/40',
      bio: 'مدرب معتمد في البرمجة',
      createdAt: new Date().toISOString()
    },
    {
      id: '3',
      email: '<EMAIL>',
      firstName: 'فاطمة',
      lastName: 'علي',
      role: 'student',
      isActive: true,
      avatarUrl: 'https://via.placeholder.com/40',
      createdAt: new Date().toISOString()
    }
  ],
  courses: [
    {
      id: '1',
      instructorId: '2',
      title: 'مقدمة في البرمجة',
      description: 'تعلم أساسيات البرمجة من الصفر باستخدام JavaScript. هذه الدورة مصممة للمبتدئين الذين يريدون دخول عالم البرمجة.',
      category: 'programming',
      difficultyLevel: 'beginner',
      estimatedDuration: 480,
      price: 0,
      thumbnailUrl: 'https://via.placeholder.com/300x200?text=دورة+البرمجة',
      isPublished: true,
      instructor: {
        id: '2',
        firstName: 'أحمد',
        lastName: 'محمد',
        avatarUrl: 'https://via.placeholder.com/40',
        bio: 'مدرب معتمد في البرمجة'
      },
      sections: [
        {
          id: '1',
          title: 'الأساسيات',
          description: 'تعلم الأساسيات الضرورية للبرمجة',
          orderIndex: 1,
          videos: [
            {
              id: '1',
              title: 'مقدمة عن البرمجة',
              description: 'تعرف على أساسيات البرمجة وأهميتها في العصر الحديث',
              duration: 600,
              orderIndex: 1,
              isPreview: true,
              videoUrl: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
              thumbnailUrl: 'https://via.placeholder.com/300x200?text=فيديو+1',
              isCompleted: false,
              pdfs: [
                {
                  id: '1',
                  title: 'ملخص الدرس الأول',
                  description: 'ملخص شامل لأساسيات البرمجة',
                  pdfUrl: 'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf',
                  fileSize: 1024000
                }
              ]
            }
          ]
        }
      ],
      progress: 25,
      isEnrolled: true,
      createdAt: new Date().toISOString()
    }
  ],
  quizzes: [
    {
      id: '1',
      courseId: '1',
      title: 'اختبار الأساسيات',
      description: 'اختبر معرفتك بأساسيات البرمجة',
      questions: [
        {
          type: 'multiple_choice',
          question: 'ما هي لغة البرمجة؟',
          options: ['أداة للتواصل مع الحاسوب', 'نوع من الطعام', 'لعبة فيديو', 'برنامج تلفزيوني'],
          correctAnswer: 0
        }
      ],
      passingScore: 70,
      timeLimit: 30,
      maxAttempts: 3,
      isActive: true,
      studentAttempts: []
    }
  ]
};

// Health check
app.get('/health', (req, res) => {
  res.json({
    success: true,
    status: 'OK',
    timestamp: new Date().toISOString(),
    message: 'منصة الدورات التدريبية - Firebase Functions'
  });
});

// Auth routes
app.post('/auth/login', (req, res) => {
  const { idToken } = req.body;
  
  if (idToken) {
    const user = mockData.users[0];
    res.json({
      success: true,
      data: {
        user: user,
        jwtToken: 'mock-jwt-token-' + user.id
      }
    });
  } else {
    res.status(401).json({
      success: false,
      message: 'بيانات الدخول غير صحيحة'
    });
  }
});

app.post('/auth/register', (req, res) => {
  const { email, firstName, lastName, role } = req.body;
  const newUser = {
    id: String(mockData.users.length + 1),
    email,
    firstName,
    lastName,
    role: role || 'student',
    isActive: true,
    avatarUrl: 'https://via.placeholder.com/40',
    createdAt: new Date().toISOString()
  };
  
  mockData.users.push(newUser);
  
  res.json({
    success: true,
    data: {
      user: newUser,
      jwtToken: 'mock-jwt-token-' + newUser.id
    }
  });
});

app.get('/auth/profile', (req, res) => {
  res.json({
    success: true,
    data: {
      user: mockData.users[0]
    }
  });
});

// Courses routes
app.get('/courses', (req, res) => {
  res.json({
    success: true,
    data: {
      courses: mockData.courses
    }
  });
});

app.get('/courses/:id', (req, res) => {
  const course = mockData.courses.find(c => c.id === req.params.id);
  if (course) {
    res.json({
      success: true,
      data: {
        course: course
      }
    });
  } else {
    res.status(404).json({
      success: false,
      message: 'الدورة غير موجودة'
    });
  }
});

// Videos routes
app.get('/videos/:id', (req, res) => {
  let video = null;
  for (const course of mockData.courses) {
    for (const section of course.sections || []) {
      video = section.videos?.find(v => v.id === req.params.id);
      if (video) break;
    }
    if (video) break;
  }
  
  if (video) {
    res.json({
      success: true,
      data: {
        video: video
      }
    });
  } else {
    res.status(404).json({
      success: false,
      message: 'الفيديو غير موجود'
    });
  }
});

// Quizzes routes
app.get('/quizzes/course/:courseId', (req, res) => {
  const quizzes = mockData.quizzes.filter(q => q.courseId === req.params.courseId);
  res.json({
    success: true,
    data: {
      quizzes: quizzes
    }
  });
});

app.get('/quizzes/:id', (req, res) => {
  const quiz = mockData.quizzes.find(q => q.id === req.params.id);
  if (quiz) {
    res.json({
      success: true,
      data: {
        quiz: quiz
      }
    });
  } else {
    res.status(404).json({
      success: false,
      message: 'الاختبار غير موجود'
    });
  }
});

app.post('/quizzes/:id/submit', (req, res) => {
  const { answers, timeTaken } = req.body;
  const quiz = mockData.quizzes.find(q => q.id === req.params.id);
  
  if (quiz) {
    let correctAnswers = 0;
    answers.forEach((answer, index) => {
      if (quiz.questions[index] && answer === quiz.questions[index].correctAnswer) {
        correctAnswers++;
      }
    });
    
    const score = Math.round((correctAnswers / quiz.questions.length) * 100);
    const passed = score >= quiz.passingScore;
    
    const attempt = {
      id: Date.now().toString(),
      score: score,
      passed: passed,
      timeTaken: timeTaken,
      completedAt: new Date().toISOString()
    };
    
    res.json({
      success: true,
      message: 'تم تسليم الاختبار بنجاح',
      data: {
        attempt: attempt
      }
    });
  } else {
    res.status(404).json({
      success: false,
      message: 'الاختبار غير موجود'
    });
  }
});

// Error handler
app.use((err, req, res, next) => {
  console.error('Error:', err);
  res.status(err.status || 500).json({
    success: false,
    error: 'Internal server error'
  });
});

// Export the Express app as a Firebase Function
exports.api = functions.https.onRequest(app);
