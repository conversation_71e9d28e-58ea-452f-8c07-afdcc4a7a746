const { supabase } = require('../config/database');

class Quiz {
  constructor(data) {
    this.id = data.id;
    this.courseId = data.course_id;
    this.title = data.title;
    this.description = data.description;
    this.questions = data.questions;
    this.passingScore = data.passing_score;
    this.timeLimit = data.time_limit;
    this.maxAttempts = data.max_attempts;
    this.isActive = data.is_active;
    this.createdAt = data.created_at;
    this.updatedAt = data.updated_at;
    
    // Additional fields from joins
    this.course = data.course;
    this.attempts = data.attempts;
    this.studentAttempts = data.student_attempts;
  }

  static async findAll(courseId, options = {}) {
    const { includeAttempts = false, studentId = null } = options;

    let query = supabase
      .from('quizzes')
      .select(`
        *,
        course:courses!quizzes_course_id_fkey(id, title)
      `)
      .eq('course_id', courseId)
      .eq('is_active', true)
      .order('created_at', { ascending: true });

    if (includeAttempts && studentId) {
      query = query.select(`
        *,
        course:courses!quizzes_course_id_fkey(id, title),
        student_attempts:quiz_attempts!quiz_attempts_quiz_id_fkey(id, score, passed, completed_at)
      `);
    }

    const { data, error } = await query;

    if (error) throw error;

    return data.map(quiz => new Quiz(quiz));
  }

  static async findById(id, options = {}) {
    const { includeAttempts = false, studentId = null } = options;

    let query = supabase
      .from('quizzes')
      .select(`
        *,
        course:courses!quizzes_course_id_fkey(id, title, instructor_id)
      `)
      .eq('id', id);

    if (includeAttempts && studentId) {
      query = query.select(`
        *,
        course:courses!quizzes_course_id_fkey(id, title, instructor_id),
        student_attempts:quiz_attempts!quiz_attempts_quiz_id_fkey(id, score, passed, completed_at)
      `);
    }

    const { data, error } = await query.single();

    if (error) {
      if (error.code === 'PGRST116') return null;
      throw error;
    }

    return new Quiz(data);
  }

  static async create(quizData) {
    const { data, error } = await supabase
      .from('quizzes')
      .insert({
        course_id: quizData.courseId,
        title: quizData.title,
        description: quizData.description,
        questions: quizData.questions,
        passing_score: quizData.passingScore || 70,
        time_limit: quizData.timeLimit,
        max_attempts: quizData.maxAttempts || 3,
        is_active: quizData.isActive !== undefined ? quizData.isActive : true
      })
      .select()
      .single();

    if (error) throw error;

    return new Quiz(data);
  }

  async update(updateData) {
    const { data, error } = await supabase
      .from('quizzes')
      .update({
        title: updateData.title || this.title,
        description: updateData.description || this.description,
        questions: updateData.questions || this.questions,
        passing_score: updateData.passingScore !== undefined ? updateData.passingScore : this.passingScore,
        time_limit: updateData.timeLimit !== undefined ? updateData.timeLimit : this.timeLimit,
        max_attempts: updateData.maxAttempts !== undefined ? updateData.maxAttempts : this.maxAttempts,
        is_active: updateData.isActive !== undefined ? updateData.isActive : this.isActive
      })
      .eq('id', this.id)
      .select()
      .single();

    if (error) throw error;

    Object.assign(this, new Quiz(data));
    return this;
  }

  async delete() {
    const { error } = await supabase
      .from('quizzes')
      .update({ is_active: false })
      .eq('id', this.id);

    if (error) throw error;
    
    this.isActive = false;
    return this;
  }

  async getAttempts(studentId = null) {
    let query = supabase
      .from('quiz_attempts')
      .select(`
        *,
        student:users!quiz_attempts_student_id_fkey(id, first_name, last_name, email)
      `)
      .eq('quiz_id', this.id)
      .order('completed_at', { ascending: false });

    if (studentId) {
      query = query.eq('student_id', studentId);
    }

    const { data, error } = await query;

    if (error) throw error;
    return data;
  }

  async getStudentAttemptCount(studentId) {
    const { count, error } = await supabase
      .from('quiz_attempts')
      .select('*', { count: 'exact', head: true })
      .eq('quiz_id', this.id)
      .eq('student_id', studentId);

    if (error) throw error;
    return count;
  }

  async canStudentAttempt(studentId) {
    const attemptCount = await this.getStudentAttemptCount(studentId);
    return attemptCount < this.maxAttempts;
  }

  async submitAttempt(studentId, answers, timeTaken = null) {
    // Check if student can attempt
    const canAttempt = await this.canStudentAttempt(studentId);
    if (!canAttempt) {
      throw new Error('Maximum attempts exceeded');
    }

    // Calculate score
    const score = this.calculateScore(answers);
    const passed = score >= this.passingScore;

    const { data, error } = await supabase
      .from('quiz_attempts')
      .insert({
        quiz_id: this.id,
        student_id: studentId,
        answers: answers,
        score: score,
        passed: passed,
        time_taken: timeTaken
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  calculateScore(answers) {
    if (!this.questions || this.questions.length === 0) return 0;

    let correctAnswers = 0;
    const totalQuestions = this.questions.length;

    this.questions.forEach((question, index) => {
      const studentAnswer = answers[index];
      
      switch (question.type) {
        case 'multiple_choice':
          if (studentAnswer === question.correctAnswer) {
            correctAnswers++;
          }
          break;
        case 'true_false':
          if (studentAnswer === question.correctAnswer) {
            correctAnswers++;
          }
          break;
        case 'short_answer':
          // Simple string comparison (case-insensitive)
          if (studentAnswer && 
              studentAnswer.toLowerCase().trim() === 
              question.correctAnswer.toLowerCase().trim()) {
            correctAnswers++;
          }
          break;
        default:
          // Unknown question type, skip
          break;
      }
    });

    return Math.round((correctAnswers / totalQuestions) * 100);
  }

  // Get quiz for student (without correct answers)
  getStudentVersion() {
    const studentQuestions = this.questions.map(question => {
      const { correctAnswer, explanation, ...studentQuestion } = question;
      return studentQuestion;
    });

    return {
      ...this.toJSON(),
      questions: studentQuestions
    };
  }

  // Get quiz statistics for instructor
  async getStatistics() {
    const { data: attempts, error } = await supabase
      .from('quiz_attempts')
      .select('score, passed, time_taken')
      .eq('quiz_id', this.id);

    if (error) throw error;

    if (attempts.length === 0) {
      return {
        totalAttempts: 0,
        averageScore: 0,
        passRate: 0,
        averageTime: 0
      };
    }

    const totalAttempts = attempts.length;
    const averageScore = attempts.reduce((sum, attempt) => sum + attempt.score, 0) / totalAttempts;
    const passedAttempts = attempts.filter(attempt => attempt.passed).length;
    const passRate = (passedAttempts / totalAttempts) * 100;
    
    const timeTakenAttempts = attempts.filter(attempt => attempt.time_taken !== null);
    const averageTime = timeTakenAttempts.length > 0 
      ? timeTakenAttempts.reduce((sum, attempt) => sum + attempt.time_taken, 0) / timeTakenAttempts.length
      : 0;

    return {
      totalAttempts,
      averageScore: Math.round(averageScore * 100) / 100,
      passRate: Math.round(passRate * 100) / 100,
      averageTime: Math.round(averageTime)
    };
  }

  toJSON() {
    return {
      id: this.id,
      courseId: this.courseId,
      course: this.course,
      title: this.title,
      description: this.description,
      questions: this.questions,
      passingScore: this.passingScore,
      timeLimit: this.timeLimit,
      maxAttempts: this.maxAttempts,
      isActive: this.isActive,
      attempts: this.attempts,
      studentAttempts: this.studentAttempts,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    };
  }
}

module.exports = Quiz;
