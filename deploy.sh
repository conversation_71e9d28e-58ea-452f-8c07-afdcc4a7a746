#!/bin/bash

echo "🚀 =========================================="
echo "🎓 نشر منصة الدورات التدريبية على Firebase"
echo "🚀 =========================================="

# التحقق من وجود Firebase CLI
if ! command -v firebase &> /dev/null; then
    echo "❌ Firebase CLI غير مثبت"
    echo "📥 يرجى تثبيت Firebase CLI أولاً:"
    echo "npm install -g firebase-tools"
    exit 1
fi

echo "✅ Firebase CLI موجود"

# التحقق من تسجيل الدخول
echo "🔐 التحقق من تسجيل الدخول..."
if ! firebase projects:list &> /dev/null; then
    echo "❌ يرجى تسجيل الدخول إلى Firebase أولاً:"
    echo "firebase login"
    exit 1
fi

echo "✅ تم تسجيل الدخول بنجاح"

# التحقق من المشروع
echo "📋 التحقق من المشروع..."
if ! firebase use --project marketwise-academy-qhizq &> /dev/null; then
    echo "❌ لا يمكن الوصول للمشروع marketwise-academy-qhizq"
    echo "يرجى التأكد من الصلاحيات"
    exit 1
fi

echo "✅ تم تحديد المشروع: marketwise-academy-qhizq"

# تثبيت التبعيات للـ Functions
echo "📦 تثبيت تبعيات Firebase Functions..."
cd backend
if [ -f "package.json" ]; then
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ فشل في تثبيت التبعيات"
        exit 1
    fi
    echo "✅ تم تثبيت التبعيات بنجاح"
else
    echo "❌ ملف package.json غير موجود في مجلد backend"
    exit 1
fi

cd ..

# بناء المشروع
echo "🔨 بناء المشروع..."
cd backend
npm run build
if [ $? -ne 0 ]; then
    echo "❌ فشل في بناء المشروع"
    exit 1
fi
cd ..

echo "✅ تم بناء المشروع بنجاح"

# نشر المشروع
echo "🚀 نشر المشروع على Firebase..."
firebase deploy

if [ $? -eq 0 ]; then
    echo "🚀 =========================================="
    echo "✅ تم النشر بنجاح!"
    echo "🌐 الرابط: https://marketwise-academy-qhizq.web.app"
    echo "📊 لوحة التحكم: https://console.firebase.google.com/project/marketwise-academy-qhizq"
    echo "🚀 =========================================="
else
    echo "❌ فشل في النشر"
    echo "يرجى مراجعة الأخطاء أعلاه"
    exit 1
fi
